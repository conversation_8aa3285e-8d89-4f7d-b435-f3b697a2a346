/* 管理系统布局样式 */
.admin-layout {
  height: 100vh;
  overflow: hidden;
}

/* 侧边栏样式 */
.admin-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.admin-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Logo区域 */
.admin-logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 8px;
}

.admin-logo img {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.admin-logo span {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
}

/* 菜单样式 */
.admin-menu {
  flex: 1;
  border-right: none;
}

.admin-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.admin-menu .ant-menu-item-selected {
  background: #2b85e4 !important;
}

.admin-menu .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.admin-menu .ant-menu-item-selected:hover {
  background: #2b85e4 !important;
}

/* 顶部导航样式 */
.admin-header {
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 99;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-btn {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-info:hover {
  background: rgba(0, 0, 0, 0.04);
}

.username {
  font-size: 14px;
  color: #333;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 内容区域样式 */
.admin-content {
  padding: 24px;
  background: #f5f5f5;
  overflow-y: auto;
  height: calc(100vh - 64px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-header {
    padding: 0 16px;
  }
  
  .admin-content {
    padding: 16px;
  }
  
  .username {
    display: none;
  }
  
  .admin-logo span {
    display: none;
  }
}
