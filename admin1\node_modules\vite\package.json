{"name": "vite", "version": "6.3.5", "type": "module", "license": "MIT", "author": "<PERSON>", "description": "Native-ESM powered web dev build tool", "bin": {"vite": "bin/vite.js"}, "keywords": ["frontend", "framework", "hmr", "dev-server", "build-tool", "vite"], "main": "./dist/node/index.js", "types": "./dist/node/index.d.ts", "exports": {".": {"module-sync": "./dist/node/index.js", "import": "./dist/node/index.js", "require": "./index.cjs"}, "./client": {"types": "./client.d.ts"}, "./module-runner": "./dist/node/module-runner.js", "./dist/client/*": "./dist/client/*", "./types/*": {"types": "./types/*"}, "./types/internal/*": null, "./package.json": "./package.json"}, "typesVersions": {"*": {"module-runner": ["dist/node/module-runner.d.ts"]}}, "imports": {"#module-sync-enabled": {"module-sync": "./misc/true.js", "default": "./misc/false.js"}}, "files": ["bin", "dist", "misc/**/*.js", "client.d.ts", "index.cjs", "index.d.cts", "types"], "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://vite.dev", "funding": "https://github.com/vitejs/vite?sponsor=1", "//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "devDependencies": {"@ampproject/remapping": "^2.3.0", "@babel/parser": "^7.27.0", "@jridgewell/trace-mapping": "^0.3.25", "@polka/compression": "^1.0.0-next.25", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-dynamic-import-vars": "2.1.4", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "16.0.1", "@rollup/pluginutils": "^5.1.4", "@types/escape-html": "^1.0.4", "@types/pnpapi": "^0.0.5", "artichokie": "^0.3.1", "cac": "^6.7.14", "chokidar": "^3.6.0", "connect": "^3.7.0", "convert-source-map": "^2.0.0", "cors": "^2.8.5", "cross-spawn": "^7.0.6", "debug": "^4.4.0", "dep-types": "link:./src/types", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "es-module-lexer": "^1.6.0", "escape-html": "^1.0.3", "estree-walker": "^3.0.3", "etag": "^1.8.1", "http-proxy": "^1.18.1", "launch-editor-middleware": "^2.10.0", "lightningcss": "^1.29.3", "magic-string": "^0.30.17", "mlly": "^1.7.4", "mrmime": "^2.0.1", "nanoid": "^5.1.5", "open": "^10.1.1", "parse5": "^7.2.1", "pathe": "^2.0.3", "periscopic": "^4.0.2", "picocolors": "^1.1.1", "postcss-import": "^16.1.0", "postcss-load-config": "^6.0.1", "postcss-modules": "^6.0.1", "resolve.exports": "^2.0.3", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-esbuild": "^6.2.1", "rollup-plugin-license": "^3.6.0", "sass": "^1.86.3", "sass-embedded": "^1.86.3", "sirv": "^3.0.1", "source-map-support": "^0.5.21", "strip-literal": "^3.0.0", "terser": "^5.39.0", "tsconfck": "^3.1.5", "tslib": "^2.8.1", "types": "link:./types", "ufo": "^1.6.1", "ws": "^8.18.1"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "less": {"optional": true}, "sugarss": {"optional": true}, "lightningcss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}, "scripts": {"dev": "tsx scripts/dev.ts", "build": "premove dist && pnpm build-bundle && pnpm build-types", "build-bundle": "rollup --config rollup.config.ts --configPlugin esbuild", "build-types": "pnpm build-types-temp && pnpm build-types-roll && pnpm build-types-check", "build-types-temp": "tsc --emitDeclarationOnly --outDir temp -p src/node/tsconfig.build.json", "build-types-roll": "rollup --config rollup.dts.config.ts --configPlugin esbuild && premove temp", "build-types-check": "tsc --project tsconfig.check.json", "typecheck": "tsc --noEmit && tsc --noEmit -p src/node", "lint": "eslint --cache --ext .ts src/**", "format": "prettier --write --cache --parser typescript \"src/**/*.ts\""}}