/* 登录页面样式 */
.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 背景 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2b85e4 0%, #6ba7f0 100%);
  z-index: 1;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

/* 登录卡片 */
.login-card {
  width: 400px;
  max-width: 90vw;
  position: relative;
  z-index: 2;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
}

.login-card .ant-card-body {
  padding: 40px 32px 32px;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  margin-bottom: 16px;
}

.login-logo img {
  width: 64px;
  height: 64px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 登录表单 */
.login-form {
  margin-top: 24px;
}

.login-form .ant-form-item {
  margin-bottom: 20px;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-input {
  height: 48px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused,
.login-form .ant-input:focus {
  border-color: #2b85e4;
  box-shadow: 0 0 0 2px rgba(43, 133, 228, 0.1);
}

.login-form .ant-input-prefix {
  color: #999;
  margin-right: 12px;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-password {
  color: #2b85e4;
  font-size: 14px;
  text-decoration: none;
}

.forgot-password:hover {
  color: #1a6bc4;
  text-decoration: underline;
}

/* 登录按钮 */
.login-button {
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #2b85e4 0%, #6ba7f0 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(43, 133, 228, 0.3);
  transition: all 0.3s;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(43, 133, 228, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

/* 登录底部 */
.login-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.login-footer p {
  font-size: 12px;
  color: #999;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    width: 100%;
    margin: 20px;
    border-radius: 12px;
  }
  
  .login-card .ant-card-body {
    padding: 32px 24px 24px;
  }
  
  .login-title {
    font-size: 20px;
  }
}
