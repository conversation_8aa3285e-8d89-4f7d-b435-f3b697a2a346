import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Avatar,
  Modal,
  Form,
  message,
  Popconfirm,
  Tooltip,
} from 'antd'
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  StopOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons'
import { formatTime, getRoleLabel } from '@/utils'
import type { User, PaginationParams } from '@/types'
import type { ColumnsType } from 'antd/es/table'
import './index.css'

const { Option } = Select

const Users: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    role: '',
    status: '',
  })
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [form] = Form.useForm()

  // 模拟用户数据
  const mockUsers: User[] = [
    {
      id: '1',
      username: 'admin',
      avatar: '',
      phone: '13800138001',
      email: '<EMAIL>',
      role: 'admin',
      gender: 'male',
      bio: '系统管理员',
      school: '示例大学',
      department: '计算机学院',
      isDisabled: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T00:00:00.000Z',
    },
    {
      id: '2',
      username: 'teacher01',
      avatar: '',
      phone: '13800138002',
      email: '<EMAIL>',
      role: 'teacher',
      gender: 'female',
      bio: '计算机科学教师',
      school: '示例大学',
      department: '计算机学院',
      isDisabled: false,
      createdAt: '2024-01-02T00:00:00.000Z',
      updatedAt: '2024-01-15T00:00:00.000Z',
    },
    {
      id: '3',
      username: 'student01',
      avatar: '',
      phone: '13800138003',
      email: '<EMAIL>',
      role: 'student',
      gender: 'male',
      bio: '计算机专业学生',
      school: '示例大学',
      department: '计算机学院',
      isDisabled: false,
      createdAt: '2024-01-03T00:00:00.000Z',
      updatedAt: '2024-01-15T00:00:00.000Z',
    },
  ]

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <div className="user-info-cell">
          <Avatar
            size={40}
            src={record.avatar}
            icon={<UserOutlined />}
            className="user-avatar"
          />
          <div className="user-details">
            <div className="username">{record.username}</div>
            <div className="user-meta">{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role: string) => {
        const colorMap: Record<string, string> = {
          admin: 'red',
          teacher: 'blue',
          student: 'green',
        }
        return <Tag color={colorMap[role]}>{getRoleLabel(role)}</Tag>
      },
    },
    {
      title: '学校/院系',
      key: 'school',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.school}</div>
          <div className="text-secondary">{record.department}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isDisabled',
      key: 'status',
      width: 80,
      render: (isDisabled: boolean) => (
        <Tag color={isDisabled ? 'red' : 'green'}>
          {isDisabled ? '已禁用' : '正常'}
        </Tag>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (time: string) => formatTime(time, 'YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={record.isDisabled ? '启用' : '禁用'}>
            <Popconfirm
              title={`确定要${record.isDisabled ? '启用' : '禁用'}该用户吗？`}
              onConfirm={() => handleToggleStatus(record)}
            >
              <Button
                type="text"
                icon={record.isDisabled ? <CheckCircleOutlined /> : <StopOutlined />}
                danger={!record.isDisabled}
              />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除该用户吗？此操作不可恢复！"
              onConfirm={() => handleDelete(record)}
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 加载用户数据
  const loadUsers = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      setTimeout(() => {
        setUsers(mockUsers)
        setPagination(prev => ({ ...prev, total: mockUsers.length }))
        setLoading(false)
      }, 500)
    } catch (error) {
      message.error('加载用户数据失败')
      setLoading(false)
    }
  }

  // 搜索用户
  const handleSearch = () => {
    loadUsers()
  }

  // 重置搜索
  const handleReset = () => {
    setSearchParams({
      keyword: '',
      role: '',
      status: '',
    })
    loadUsers()
  }

  // 编辑用户
  const handleEdit = (user: User) => {
    setEditingUser(user)
    form.setFieldsValue(user)
    setEditModalVisible(true)
  }

  // 保存编辑
  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      // 模拟保存
      message.success('用户信息更新成功')
      setEditModalVisible(false)
      loadUsers()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 切换用户状态
  const handleToggleStatus = (user: User) => {
    // 模拟API调用
    message.success(`用户${user.isDisabled ? '启用' : '禁用'}成功`)
    loadUsers()
  }

  // 删除用户
  const handleDelete = (user: User) => {
    // 模拟API调用
    message.success('用户删除成功')
    loadUsers()
  }

  // 表格分页变化
  const handleTableChange = (pagination: any) => {
    setPagination(pagination)
    loadUsers()
  }

  useEffect(() => {
    loadUsers()
  }, [])

  return (
    <div className="users-page">
      {/* 搜索区域 */}
      <Card className="search-card mb-16">
        <div className="search-form">
          <Space size="middle" wrap>
            <Input
              placeholder="搜索用户名、邮箱、手机号"
              prefix={<SearchOutlined />}
              value={searchParams.keyword}
              onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
              style={{ width: 250 }}
            />
            <Select
              placeholder="选择角色"
              value={searchParams.role}
              onChange={(value) => setSearchParams(prev => ({ ...prev, role: value }))}
              style={{ width: 120 }}
              allowClear
            >
              <Option value="admin">管理员</Option>
              <Option value="teacher">教师</Option>
              <Option value="student">学生</Option>
            </Select>
            <Select
              placeholder="选择状态"
              value={searchParams.status}
              onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
              style={{ width: 120 }}
              allowClear
            >
              <Option value="normal">正常</Option>
              <Option value="disabled">已禁用</Option>
            </Select>
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>
      </Card>

      {/* 用户列表 */}
      <Card
        title="用户列表"
        extra={
          <Button type="primary" icon={<PlusOutlined />}>
            新增用户
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 编辑用户模态框 */}
      <Modal
        title="编辑用户"
        open={editModalVisible}
        onOk={handleSave}
        onCancel={() => setEditModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={editingUser || {}}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="phone"
            label="手机号"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select>
              <Option value="admin">管理员</Option>
              <Option value="teacher">教师</Option>
              <Option value="student">学生</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="school"
            label="学校"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="department"
            label="院系"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="bio"
            label="个人简介"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Users
