/* 全局动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes slideRight {
  from { transform: translateX(-10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideLeft {
  from { transform: translateX(10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* 新增动画效果 */
@keyframes floatUp {
  0% { transform: translateY(0); }
  50% { transform: translateY(-6px); }
  100% { transform: translateY(0); }
}

@keyframes rotateIn {
  from { transform: rotateY(-90deg); opacity: 0; }
  to { transform: rotateY(0); opacity: 1; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes cardHover {
  0% { transform: translateY(0) scale(1); box-shadow: var(--card-shadow); }
  100% { transform: translateY(-5px) scale(1.01); box-shadow: 0 15px 35px rgba(74, 144, 226, 0.12); }
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes rippleEffect {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(2.5); opacity: 0; }
}

/* 全局主题变量 */
:root {
  /* 颜色系统 - 增强色彩方案 */
  --primary-color: #4A90E2;
  --primary-light: #6AB6F7;
  --primary-gradient: linear-gradient(120deg, #4A90E2, #6AB6F7);
  --primary-gradient-alt: linear-gradient(135deg, #4A90E2, #5C6BC0);
  --primary-shimmer: linear-gradient(90deg, var(--primary-light) 0%, var(--primary-color) 50%, var(--primary-light) 100%);
  --secondary-color: #F8F9FC;
  --secondary-gradient: linear-gradient(to bottom, #FFFFFF, #F8F9FC);
  --text-color: #333333;
  --text-color-secondary: #8E9AAA;
  --text-color-light: #FFFFFF;
  --border-color: rgba(74, 144, 226, 0.08);
  --success-color: #2ECC71;
  --error-color: #FF4757;
  --warning-color: #FFC82C;
  --info-color: #4A90E2;
  
  /* 尺寸与间距 */
  --border-radius-sm: 8px;
  --border-radius: 16px;
  --border-radius-lg: 24px;
  --border-radius-pill: 50px;
  --padding-sm: 10px;
  --padding: 15px;
  --padding-lg: 20px;
  
  /* 阴影效果 - 增强层次感 */
  --card-shadow: 0 8px 20px rgba(74, 144, 226, 0.08);
  --card-shadow-hover: 0 15px 35px rgba(74, 144, 226, 0.15);
  --button-shadow: 0 6px 15px rgba(74, 144, 226, 0.25);
  --button-shadow-active: 0 3px 10px rgba(74, 144, 226, 0.2);
  --floating-shadow: 0 10px 25px rgba(74, 144, 226, 0.18);
  --header-shadow: 0 4px 15px rgba(74, 144, 226, 0.15);
  --input-shadow: 0 2px 8px rgba(74, 144, 226, 0.06);
  
  /* 动画时长 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* 过渡曲线 */
  --ease-out: cubic-bezier(0.25, 0.8, 0.25, 1);
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-in-out: cubic-bezier(0.42, 0, 0.58, 1);
  --ease-elastic: cubic-bezier(0.8, 0.2, 0.2, 1.2);
}

/* 全局共用样式类 */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--secondary-color);
  animation: fadeIn var(--transition-normal) var(--ease-out);
}

.card {
  background-color: #FFFFFF;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  border: none;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  animation: scaleIn var(--transition-normal) var(--ease-out);
  position: relative;
}

.card:active {
  transform: translateY(1px);
  box-shadow: 0 6px 12px rgba(74, 144, 226, 0.05);
}

/* 可悬停的卡片效果 */
.card.hover-effect {
  transition: all var(--transition-normal) var(--ease-out);
}

.card.hover-effect:active {
  animation: cardHover var(--transition-normal) forwards;
}

.gradient-header {
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientFlow 8s infinite linear, fadeIn 0.4s var(--ease-out);
  padding: 30px 20px 20px;
  position: relative;
  border-radius: 0 0 25px 25px;
  box-shadow: var(--header-shadow);
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: slideRight var(--transition-normal) var(--ease-out);
}

.header-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  animation: slideRight var(--transition-normal) var(--ease-out) 0.1s;
}

.primary-btn {
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientFlow 8s infinite linear;
  height: 54px;
  color: #FFFFFF;
  border-radius: var(--border-radius-pill);
  border: none;
  font-size: 17px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal) var(--ease-out);
  box-shadow: var(--button-shadow);
  position: relative;
  overflow: hidden;
}

.primary-btn:active {
  transform: translateY(3px) scale(0.98);
  box-shadow: var(--button-shadow-active);
}

.primary-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.primary-btn:active:before {
  opacity: 1;
}

/* 按钮波纹效果 */
.primary-btn .ripple {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: rippleEffect 0.6s var(--ease-out) forwards;
  pointer-events: none;
}

/* 输入框样式 */
.input-box {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #FFFFFF;
  border-radius: 22px;
  padding: 0 15px;
  transition: all var(--transition-fast);
  box-shadow: none;
  border: 1px solid rgba(74, 144, 226, 0.05);
}

.input-box:focus-within {
  background-color: #FFFFFF;
  border-color: rgba(74, 144, 226, 0.15);
  box-shadow: 0 3px 12px rgba(74, 144, 226, 0.1);
}

/* 输入框图标 */
.input-box .icon {
  color: var(--primary-color);
  font-size: 18px;
  margin-right: 8px;
  transition: transform var(--transition-fast);
}

.input-box:focus-within .icon {
  transform: scale(1.1);
}

/* 分隔线 */
.divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 10px 0;
  position: relative;
  overflow: hidden;
}

/* 闪光分隔线效果 */
.divider.shimmer {
  background: var(--primary-shimmer);
  background-size: 200% 100%;
  animation: shimmer 2s infinite linear;
}

/* 选项卡样式 */
.tabs {
  display: flex;
  background-color: #FFFFFF;
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px rgba(74, 144, 226, 0.05);
  overflow: hidden;
  margin-bottom: 15px;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: var(--text-color-secondary);
  position: relative;
  transition: all var(--transition-normal);
  animation: fadeIn var(--transition-normal);
}

.tab-item.active {
  color: var(--primary-color);
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: 1.5px;
  animation: scaleIn var(--transition-normal) var(--ease-bounce);
}

/* 列表项样式 */
.list-item {
  padding: 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  background-color: #FFFFFF;
  animation: fadeIn var(--transition-normal);
}

.list-item:active {
  background-color: rgba(74, 144, 226, 0.05);
  transform: translateX(2px);
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
  font-size: 12px;
  margin-right: 8px;
  transition: all var(--transition-normal);
  animation: fadeIn var(--transition-normal);
}

.tag:active {
  transform: scale(0.95);
  background-color: rgba(74, 144, 226, 0.15);
}

/* 头像样式 */
.avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  object-fit: cover;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
  animation: scaleIn var(--transition-normal) var(--ease-out);
}

.avatar:active {
  transform: scale(0.95);
}

.avatar.shimmer {
  position: relative;
  overflow: hidden;
}

.avatar.shimmer::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(30deg);
  animation: shimmer 1.5s infinite;
}

/* 浮动按钮 */
.floating-btn {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientFlow 8s infinite linear;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--floating-shadow);
  transition: all var(--transition-normal) var(--ease-bounce);
  position: relative;
  overflow: hidden;
}

.floating-btn:active {
  transform: scale(0.9);
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.1);
}

.floating-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.floating-btn:active::before {
  opacity: 1;
}

/* 大型浮动按钮 */
.floating-btn.large {
  width: 70px;
  height: 70px;
  border-radius: 35px;
  animation: floatUp 2s infinite ease-in-out, gradientFlow 8s infinite linear;
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 15px 0;
  font-size: 12px;
  color: var(--text-color-secondary);
  animation: pulse 1.5s infinite;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 2px solid rgba(74, 144, 226, 0.1);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  animation: fadeIn var(--transition-normal) var(--ease-out);
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  opacity: 0.6;
  animation: floatUp 3s infinite ease-in-out;
}

.empty-text {
  font-size: 16px;
  color: var(--text-color-secondary);
  margin-bottom: 8px;
  animation: fadeIn var(--transition-normal) var(--ease-out) 0.2s;
}

.empty-subtext {
  font-size: 14px;
  color: var(--text-color-secondary);
  opacity: 0.7;
  text-align: center;
  margin-bottom: 20px;
  animation: fadeIn var(--transition-normal) var(--ease-out) 0.3s;
}

/* 工具类 */
.animate-fadeIn { animation: fadeIn var(--transition-normal) var(--ease-out); }
.animate-scaleIn { animation: scaleIn var(--transition-normal) var(--ease-bounce); }
.animate-slideUp { animation: slideUp var(--transition-normal) var(--ease-out); }
.animate-slideRight { animation: slideRight var(--transition-normal) var(--ease-out); }
.animate-slideLeft { animation: slideLeft var(--transition-normal) var(--ease-out); }
.animate-pulse { animation: pulse 2s infinite; }
.animate-float { animation: floatUp 3s infinite ease-in-out; }
.animate-rotate { animation: rotateIn var(--transition-normal) var(--ease-out); }

/* 动画延迟类 */
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* 禁用点击状态 */
.non-clickable {
  pointer-events: none;
  opacity: 0.8;
} 