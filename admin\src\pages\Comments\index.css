/* 评论管理页面样式 */
.comments-page {
  padding: 0;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  padding: 8px 0;
}

/* 评论信息单元格 */
.comment-info-cell {
  max-width: 330px;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.comment-meta {
  flex: 1;
  min-width: 0;
}

.comment-meta .ant-typography {
  display: block;
  margin: 0;
}

.comment-time {
  font-size: 12px;
}

.comment-content {
  margin-bottom: 8px;
}

.reply-context {
  background: #f5f5f5;
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.reply-label {
  font-size: 12px;
}

.comment-text {
  margin: 0 !important;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.comment-stats {
  font-size: 12px;
  color: #666;
}

/* 帖子信息 */
.post-info {
  max-width: 180px;
}

.post-title-link {
  display: block;
  margin-bottom: 4px;
  color: #1890ff;
  cursor: pointer;
}

.post-title-link:hover {
  color: #40a9ff;
}

.post-author {
  font-size: 12px;
}

/* 操作按钮样式 */
.comments-page .ant-btn-text {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comments-page .ant-btn-text:hover {
  background: rgba(0, 0, 0, 0.04);
}

/* 评论详情模态框 */
.comment-detail {
  padding: 16px 0;
}

.comment-detail-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details {
  flex: 1;
}

.author-details .ant-typography {
  display: block;
  margin: 0;
}

.reply-context-detail {
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.original-comment {
  margin-top: 8px;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
}

.comment-detail-content {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

.comment-detail-post {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.post-info-detail {
  margin-top: 8px;
}

.post-info-detail .ant-typography {
  display: block;
  margin: 0;
}

.comment-detail-stats {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  color: #666;
  font-size: 14px;
}

/* 表格样式优化 */
.comments-page .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

.comments-page .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.comments-page .ant-table-cell {
  padding: 12px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-form .ant-space {
    width: 100%;
    justify-content: stretch;
  }
  
  .search-form .ant-input,
  .search-form .ant-select {
    width: 100% !important;
  }
  
  .comment-info-cell {
    max-width: none;
  }
  
  .post-info {
    max-width: none;
  }
  
  .comments-page .ant-table-cell {
    padding: 8px 12px;
  }
  
  .comment-author-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
