/* 系统设置页面样式 */
.settings-page {
  padding: 0;
}

.settings-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 标签页内容 */
.tab-content {
  padding: 16px 0;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.tab-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 分类信息样式 */
.category-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8px;
}

.category-details {
  flex: 1;
}

.category-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.category-desc {
  font-size: 12px;
  color: #999;
}

/* 排序拖拽样式 */
.sort-handle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: move;
}

.sort-handle .anticon {
  color: #999;
}

/* 话题信息样式 */
.topic-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.topic-name {
  font-weight: 600;
  color: #333;
}

/* 系统配置表单 */
.config-form {
  max-width: 600px;
}

.config-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.config-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #2b85e4;
  padding-bottom: 8px;
}

.config-section .ant-form-item {
  margin-bottom: 16px;
}

.config-section .ant-form-item:last-child {
  margin-bottom: 0;
}

/* 表单项样式 */
.config-form .ant-form-item-label > label {
  font-weight: 600;
  color: #333;
}

.config-form .ant-input,
.config-form .ant-input-number,
.config-form .ant-select {
  border-radius: 6px;
}

.config-form .ant-switch {
  background: #d9d9d9;
}

.config-form .ant-switch-checked {
  background: #2b85e4;
}

/* 操作按钮样式 */
.settings-page .ant-btn-text {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-page .ant-btn-text:hover {
  background: rgba(0, 0, 0, 0.04);
}

/* 表格样式优化 */
.settings-page .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

.settings-page .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.settings-page .ant-table-cell {
  padding: 12px 16px;
}

/* 模态框样式 */
.settings-page .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.settings-page .ant-modal .ant-form-item-label > label {
  font-weight: 600;
  color: #333;
}

/* 标签页样式 */
.settings-page .ant-tabs-tab {
  font-weight: 500;
}

.settings-page .ant-tabs-tab-active {
  font-weight: 600;
}

.settings-page .ant-tabs-ink-bar {
  background: #2b85e4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .category-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .config-form {
    max-width: none;
  }
  
  .config-section {
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .settings-page .ant-table-cell {
    padding: 8px 12px;
  }
  
  .settings-page .ant-tabs-tab {
    padding: 8px 12px;
  }
}
