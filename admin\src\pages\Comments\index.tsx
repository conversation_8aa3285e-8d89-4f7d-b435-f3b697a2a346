import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Popconfirm,
  Tooltip,
  Typography,
  message,
  Avatar,
} from 'antd'
import {
  SearchOutlined,
  EyeOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined,
  MessageOutlined,
} from '@ant-design/icons'
import { formatTime, getCommentStatusLabel } from '../../utils'
import type { Comment } from '../../types'
import type { ColumnsType } from 'antd/es/table'
import './index.css'

const { Option } = Select
const { Text, Paragraph } = Typography

const Comments: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [comments, setComments] = useState<Comment[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    status: '',
    postId: '',
  })
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedComment, setSelectedComment] = useState<Comment | null>(null)

  // 模拟评论数据
  const mockComments: Comment[] = [
    {
      id: '1',
      content: '这个分享很棒！我也想去看樱花。',
      authorId: '3',
      postId: '1',
      likeCount: 5,
      status: 'normal',
      createdAt: '2024-01-15T11:00:00.000Z',
      updatedAt: '2024-01-15T11:00:00.000Z',
      author: {
        id: '3',
        username: 'student02',
        avatar: '',
        role: 'student',
        isDisabled: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
      post: {
        id: '1',
        title: '校园生活分享',
        content: '今天在校园里看到了美丽的樱花...',
        authorId: '2',
        categoryId: 1,
        status: 'published',
        viewCount: 156,
        likeCount: 23,
        commentCount: 8,
        favoriteCount: 12,
        isTop: false,
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
      },
    },
    {
      id: '2',
      content: '感谢分享！这些学习方法很实用，我会试试看的。',
      authorId: '4',
      postId: '2',
      likeCount: 3,
      status: 'normal',
      createdAt: '2024-01-14T10:30:00.000Z',
      updatedAt: '2024-01-14T10:30:00.000Z',
      author: {
        id: '4',
        username: 'student03',
        avatar: '',
        role: 'student',
        isDisabled: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
      post: {
        id: '2',
        title: '学习经验分享：如何高效学习编程',
        content: '作为一名计算机专业的学生...',
        authorId: '3',
        categoryId: 2,
        status: 'published',
        viewCount: 89,
        likeCount: 15,
        commentCount: 5,
        favoriteCount: 8,
        isTop: true,
        createdAt: '2024-01-14T09:15:00.000Z',
        updatedAt: '2024-01-14T09:15:00.000Z',
      },
    },
    {
      id: '3',
      content: '什么时候开始报名？有具体的比赛规则吗？',
      authorId: '5',
      postId: '3',
      likeCount: 2,
      status: 'normal',
      createdAt: '2024-01-13T09:30:00.000Z',
      updatedAt: '2024-01-13T09:30:00.000Z',
      author: {
        id: '5',
        username: 'student04',
        avatar: '',
        role: 'student',
        isDisabled: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
      post: {
        id: '3',
        title: '社团活动通知：编程大赛报名开始',
        content: '各位同学，我们学院的编程大赛即将开始报名...',
        authorId: '1',
        categoryId: 3,
        status: 'published',
        viewCount: 234,
        likeCount: 45,
        commentCount: 12,
        favoriteCount: 28,
        isTop: true,
        createdAt: '2024-01-13T08:45:00.000Z',
        updatedAt: '2024-01-13T08:45:00.000Z',
      },
    },
    {
      id: '4',
      content: '这是一条回复评论',
      authorId: '2',
      postId: '1',
      replyTo: '1',
      likeCount: 1,
      status: 'normal',
      createdAt: '2024-01-15T12:00:00.000Z',
      updatedAt: '2024-01-15T12:00:00.000Z',
      author: {
        id: '2',
        username: 'student01',
        avatar: '',
        role: 'student',
        isDisabled: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
      post: {
        id: '1',
        title: '校园生活分享',
        content: '今天在校园里看到了美丽的樱花...',
        authorId: '2',
        categoryId: 1,
        status: 'published',
        viewCount: 156,
        likeCount: 23,
        commentCount: 8,
        favoriteCount: 12,
        isTop: false,
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
      },
      replyToComment: {
        id: '1',
        content: '这个分享很棒！我也想去看樱花。',
        authorId: '3',
        postId: '1',
        likeCount: 5,
        status: 'normal',
        createdAt: '2024-01-15T11:00:00.000Z',
        updatedAt: '2024-01-15T11:00:00.000Z',
      },
    },
  ]

  // 表格列定义
  const columns: ColumnsType<Comment> = [
    {
      title: '评论信息',
      key: 'commentInfo',
      width: 350,
      render: (_, record) => (
        <div className="comment-info-cell">
          <div className="comment-header">
            <Avatar
              size={32}
              src={record.author?.avatar}
              icon={<UserOutlined />}
            />
            <div className="comment-meta">
              <Text strong>{record.author?.username}</Text>
              <Text type="secondary" className="comment-time">
                {formatTime(record.createdAt, 'MM-DD HH:mm')}
              </Text>
            </div>
          </div>
          <div className="comment-content">
            {record.replyTo && record.replyToComment && (
              <div className="reply-context">
                <Text type="secondary" className="reply-label">
                  回复: {record.replyToComment.content}
                </Text>
              </div>
            )}
            <Paragraph
              ellipsis={{ rows: 2, tooltip: record.content }}
              className="comment-text"
            >
              {record.content}
            </Paragraph>
          </div>
          <div className="comment-stats">
            <Space size="small">
              <Text type="secondary">点赞: {record.likeCount}</Text>
            </Space>
          </div>
        </div>
      ),
    },
    {
      title: '所属帖子',
      key: 'post',
      width: 200,
      render: (_, record) => (
        <div className="post-info">
          <Text
            strong
            ellipsis={{ tooltip: record.post?.title }}
            className="post-title-link"
          >
            {record.post?.title || '未知帖子'}
          </Text>
          <Text type="secondary" className="post-author">
            作者: {record.post?.author?.username || '未知'}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colorMap: Record<string, string> = {
          normal: 'green',
          deleted: 'red',
        }
        return <Tag color={colorMap[status]}>{getCommentStatusLabel(status)}</Tag>
      },
    },
    {
      title: '评论时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (time: string) => formatTime(time, 'MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="审核通过">
            <Button
              type="text"
              icon={<CheckCircleOutlined />}
              onClick={() => handleApprove(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除该评论吗？此操作不可恢复！"
              onConfirm={() => handleDelete(record)}
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 加载评论数据
  const loadComments = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      setTimeout(() => {
        setComments(mockComments)
        setPagination(prev => ({ ...prev, total: mockComments.length }))
        setLoading(false)
      }, 500)
    } catch (error) {
      message.error('加载评论数据失败')
      setLoading(false)
    }
  }

  // 搜索评论
  const handleSearch = () => {
    loadComments()
  }

  // 重置搜索
  const handleReset = () => {
    setSearchParams({
      keyword: '',
      status: '',
      postId: '',
    })
    loadComments()
  }

  // 查看详情
  const handleViewDetail = (comment: Comment) => {
    setSelectedComment(comment)
    setDetailModalVisible(true)
  }

  // 审核通过
  const handleApprove = (comment: Comment) => {
    message.success('评论审核通过')
    loadComments()
  }

  // 删除评论
  const handleDelete = (comment: Comment) => {
    message.success('评论删除成功')
    loadComments()
  }

  // 表格分页变化
  const handleTableChange = (pagination: any) => {
    setPagination(pagination)
    loadComments()
  }

  useEffect(() => {
    loadComments()
  }, [])

  return (
    <div className="comments-page">
      {/* 搜索区域 */}
      <Card className="search-card mb-16">
        <div className="search-form">
          <Space size="middle" wrap>
            <Input
              placeholder="搜索评论内容、用户名"
              prefix={<SearchOutlined />}
              value={searchParams.keyword}
              onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
              style={{ width: 250 }}
            />
            <Select
              placeholder="选择状态"
              value={searchParams.status}
              onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
              style={{ width: 120 }}
              allowClear
            >
              <Option value="normal">正常</Option>
              <Option value="deleted">已删除</Option>
            </Select>
            <Input
              placeholder="帖子ID"
              value={searchParams.postId}
              onChange={(e) => setSearchParams(prev => ({ ...prev, postId: e.target.value }))}
              style={{ width: 120 }}
            />
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>
      </Card>

      {/* 评论列表 */}
      <Card title="评论列表">
        <Table
          columns={columns}
          dataSource={comments}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 评论详情模态框 */}
      <Modal
        title="评论详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedComment && (
          <div className="comment-detail">
            <div className="comment-detail-header">
              <div className="comment-author-info">
                <Avatar
                  size={48}
                  src={selectedComment.author?.avatar}
                  icon={<UserOutlined />}
                />
                <div className="author-details">
                  <Text strong>{selectedComment.author?.username}</Text>
                  <Text type="secondary">
                    {selectedComment.author?.role === 'admin' ? '管理员' :
                     selectedComment.author?.role === 'teacher' ? '教师' : '学生'}
                  </Text>
                  <Text type="secondary">
                    {formatTime(selectedComment.createdAt)}
                  </Text>
                </div>
              </div>
            </div>

            {selectedComment.replyTo && selectedComment.replyToComment && (
              <div className="reply-context-detail">
                <Text type="secondary">回复评论:</Text>
                <div className="original-comment">
                  {selectedComment.replyToComment.content}
                </div>
              </div>
            )}

            <div className="comment-detail-content">
              <p>{selectedComment.content}</p>
            </div>

            <div className="comment-detail-post">
              <Text type="secondary">所属帖子:</Text>
              <div className="post-info-detail">
                <Text strong>{selectedComment.post?.title}</Text>
                <Text type="secondary">
                  作者: {selectedComment.post?.author?.username}
                </Text>
              </div>
            </div>

            <div className="comment-detail-stats">
              <Space>
                <span>点赞数: {selectedComment.likeCount}</span>
                <span>状态: {getCommentStatusLabel(selectedComment.status)}</span>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Comments
