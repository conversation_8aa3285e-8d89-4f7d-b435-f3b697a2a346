import App from './App'
import imagePathHelper from './utils/imagePathHelper'

// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false

// 添加全局图片路径助手
Vue.prototype.$imageHelper = imagePathHelper
Vue.prototype.$getImage = imagePathHelper.getImage

App.mpType = 'app'
const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	
	// 添加全局图片路径助手（Vue3方式）
	app.config.globalProperties.$imageHelper = imagePathHelper
	app.config.globalProperties.$getImage = imagePathHelper.getImage
	
	return {
		app
	}
}
// #endif
