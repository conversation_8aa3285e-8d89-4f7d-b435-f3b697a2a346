import request from '@/utils/request'
import type { LoginForm, AuthUser } from '@/types'

export interface LoginResponse {
  user: AuthUser
  token: string
}

export const authService = {
  /**
   * 用户登录
   */
  login: async (data: LoginForm): Promise<LoginResponse> => {
    // 临时模拟登录，实际应该调用后端API
    if (data.username === 'admin' && data.password === 'admin123') {
      return Promise.resolve({
        user: {
          id: '1',
          username: 'admin',
          avatar: '',
          role: 'admin',
          permissions: ['user:read', 'post:read', 'comment:read', 'system:read']
        },
        token: 'mock-jwt-token-' + Date.now()
      })
    } else {
      return Promise.reject(new Error('用户名或密码错误'))
    }
    // 正式环境使用：
    // return request.post('/auth/login', data)
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: async (): Promise<AuthUser> => {
    return request.get('/auth/me')
  },

  /**
   * 刷新token
   */
  refreshToken: async (): Promise<{ token: string }> => {
    return request.post('/auth/refresh')
  },

  /**
   * 退出登录
   */
  logout: async (): Promise<void> => {
    return request.post('/auth/logout')
  },
}
