import request from '@/utils/request'
import type { <PERSON>ginForm, AuthUser } from '@/types'

export interface LoginResponse {
  user: AuthUser
  token: string
}

export const authService = {
  /**
   * 用户登录
   */
  login: async (data: LoginForm): Promise<LoginResponse> => {
    return request.post('/auth/login', data)
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: async (): Promise<AuthUser> => {
    return request.get('/auth/me')
  },

  /**
   * 刷新token
   */
  refreshToken: async (): Promise<{ token: string }> => {
    return request.post('/auth/refresh')
  },

  /**
   * 退出登录
   */
  logout: async (): Promise<void> => {
    return request.post('/auth/logout')
  },
}
