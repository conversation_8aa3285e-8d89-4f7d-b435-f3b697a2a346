import request from '../utils/request'
import type { Statistics } from '../types'

export interface DashboardStats {
  userStats: {
    total: number
    todayNew: number
    todayActive: number
    weeklyGrowth: number
  }
  postStats: {
    total: number
    todayNew: number
    published: number
    weeklyGrowth: number
  }
  commentStats: {
    total: number
    todayNew: number
    weeklyGrowth: number
  }
  engagementStats: {
    totalLikes: number
    totalFavorites: number
    avgEngagement: number
  }
}

export interface ChartData {
  name: string
  users: number
  posts: number
  comments: number
  date: string
}

export interface TopPost {
  id: string
  title: string
  author: string
  viewCount: number
  likeCount: number
  commentCount: number
  createdAt: string
}

export interface TopUser {
  id: string
  username: string
  avatar?: string
  postCount: number
  likeCount: number
  followerCount: number
}

export const dashboardService = {
  /**
   * 获取仪表板统计数据
   */
  getStats: async (): Promise<DashboardStats> => {
    // 模拟数据，实际应该调用后端API
    return Promise.resolve({
      userStats: {
        total: 1234,
        todayNew: 12,
        todayActive: 89,
        weeklyGrowth: 12.5,
      },
      postStats: {
        total: 567,
        todayNew: 23,
        published: 534,
        weeklyGrowth: 8.3,
      },
      commentStats: {
        total: 890,
        todayNew: 45,
        weeklyGrowth: -2.1,
      },
      engagementStats: {
        totalLikes: 2345,
        totalFavorites: 1234,
        avgEngagement: 4.2,
      },
    })
    // 正式环境使用：
    // return request.get('/admin/dashboard/stats')
  },

  /**
   * 获取图表数据
   */
  getChartData: async (days: number = 7): Promise<ChartData[]> => {
    // 模拟数据
    const data: ChartData[] = []
    const today = new Date()
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      
      data.push({
        name: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()],
        users: Math.floor(Math.random() * 100) + 50,
        posts: Math.floor(Math.random() * 50) + 20,
        comments: Math.floor(Math.random() * 80) + 30,
        date: date.toISOString().split('T')[0],
      })
    }
    
    return Promise.resolve(data)
    // 正式环境使用：
    // return request.get(`/admin/dashboard/chart?days=${days}`)
  },

  /**
   * 获取热门帖子
   */
  getTopPosts: async (limit: number = 10): Promise<TopPost[]> => {
    // 模拟数据
    const posts: TopPost[] = [
      {
        id: '1',
        title: '校园生活分享：春天的樱花真美',
        author: 'student01',
        viewCount: 1256,
        likeCount: 89,
        commentCount: 23,
        createdAt: '2024-01-15T10:30:00.000Z',
      },
      {
        id: '2',
        title: '学习经验分享：如何高效学习编程',
        author: 'student02',
        viewCount: 987,
        likeCount: 67,
        commentCount: 18,
        createdAt: '2024-01-14T09:15:00.000Z',
      },
      {
        id: '3',
        title: '社团活动通知：编程大赛报名开始',
        author: 'admin',
        viewCount: 2345,
        likeCount: 156,
        commentCount: 45,
        createdAt: '2024-01-13T08:45:00.000Z',
      },
    ]
    
    return Promise.resolve(posts.slice(0, limit))
    // 正式环境使用：
    // return request.get(`/admin/dashboard/top-posts?limit=${limit}`)
  },

  /**
   * 获取活跃用户
   */
  getTopUsers: async (limit: number = 10): Promise<TopUser[]> => {
    // 模拟数据
    const users: TopUser[] = [
      {
        id: '1',
        username: 'student01',
        avatar: '',
        postCount: 25,
        likeCount: 234,
        followerCount: 89,
      },
      {
        id: '2',
        username: 'student02',
        avatar: '',
        postCount: 18,
        likeCount: 189,
        followerCount: 67,
      },
      {
        id: '3',
        username: 'teacher01',
        avatar: '',
        postCount: 12,
        likeCount: 156,
        followerCount: 123,
      },
    ]
    
    return Promise.resolve(users.slice(0, limit))
    // 正式环境使用：
    // return request.get(`/admin/dashboard/top-users?limit=${limit}`)
  },

  /**
   * 获取实时数据
   */
  getRealTimeData: async () => {
    // 模拟实时数据
    return Promise.resolve({
      onlineUsers: Math.floor(Math.random() * 50) + 20,
      todayVisits: Math.floor(Math.random() * 1000) + 500,
      todayPosts: Math.floor(Math.random() * 50) + 10,
      todayComments: Math.floor(Math.random() * 100) + 30,
    })
    // 正式环境使用：
    // return request.get('/admin/dashboard/realtime')
  },
}
