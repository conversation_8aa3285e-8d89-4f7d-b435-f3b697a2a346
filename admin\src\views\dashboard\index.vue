<template>
  <div class="dashboard">
    <!-- 实时数据卡片 -->
    <el-card class="realtime-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>实时数据</span>
          <el-button 
            type="primary" 
            :icon="Refresh" 
            :loading="refreshLoading"
            @click="refreshData"
            size="small"
          >
            刷新
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" v-for="item in realtimeData" :key="item.label">
          <div class="realtime-item">
            <div class="realtime-value">{{ item.value }}</div>
            <div class="realtime-label">{{ item.label }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :xs="24" :sm="12" :lg="6" v-for="stat in statistics" :key="stat.title">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon><component :is="stat.icon" /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
              <div class="stat-trend" :class="stat.trend > 0 ? 'trend-up' : 'trend-down'">
                <el-icon>
                  <ArrowUp v-if="stat.trend > 0" />
                  <ArrowDown v-else />
                </el-icon>
                {{ Math.abs(stat.trend) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="16" class="charts-row">
      <el-col :xs="24" :lg="16">
        <el-card title="用户活跃度趋势" shadow="hover">
          <div ref="userChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="8">
        <el-card title="内容统计" shadow="hover">
          <div ref="contentChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 热门内容 -->
    <el-row :gutter="16" class="content-row">
      <el-col :xs="24" :lg="12">
        <el-card title="热门帖子" shadow="hover">
          <div class="content-list">
            <div 
              v-for="(post, index) in topPosts" 
              :key="post.id"
              class="content-item"
            >
              <div class="rank">{{ index + 1 }}</div>
              <div class="content-info">
                <div class="content-title">{{ post.title }}</div>
                <div class="content-meta">
                  <span><el-icon><View /></el-icon> {{ post.viewCount }}</span>
                  <span><el-icon><Star /></el-icon> {{ post.likeCount }}</span>
                  <span><el-icon><ChatDotRound /></el-icon> {{ post.commentCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card title="活跃用户" shadow="hover">
          <div class="content-list">
            <div 
              v-for="(user, index) in topUsers" 
              :key="user.id"
              class="content-item"
            >
              <div class="rank">{{ index + 1 }}</div>
              <div class="content-info">
                <div class="content-title">
                  <el-avatar :size="24" :src="user.avatar">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                  {{ user.username }}
                </div>
                <div class="content-meta">
                  <span><el-icon><Document /></el-icon> {{ user.postCount }}帖</span>
                  <span><el-icon><Star /></el-icon> {{ user.likeCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 响应式数据
const refreshLoading = ref(false)
const userChartRef = ref<HTMLDivElement>()
const contentChartRef = ref<HTMLDivElement>()

// 实时数据
const realtimeData = ref([
  { label: '在线用户', value: 42 },
  { label: '今日访问', value: 1256 },
  { label: '今日发帖', value: 23 },
  { label: '今日评论', value: 67 }
])

// 统计数据
const statistics = ref([
  {
    title: '总用户数',
    value: 1234,
    icon: 'User',
    color: '#409EFF',
    trend: 12.5
  },
  {
    title: '总帖子数',
    value: 567,
    icon: 'Document',
    color: '#67C23A',
    trend: 8.3
  },
  {
    title: '总评论数',
    value: 890,
    icon: 'ChatDotRound',
    color: '#E6A23C',
    trend: -2.1
  },
  {
    title: '今日活跃',
    value: 89,
    icon: 'View',
    color: '#F56C6C',
    trend: 15.2
  }
])

// 热门帖子
const topPosts = ref([
  { id: '1', title: '校园生活分享：春天的樱花', viewCount: 1256, likeCount: 89, commentCount: 23 },
  { id: '2', title: '学习经验：如何高效学习编程', viewCount: 987, likeCount: 67, commentCount: 18 },
  { id: '3', title: '社团活动：编程大赛报名开始', viewCount: 2345, likeCount: 156, commentCount: 45 }
])

// 活跃用户
const topUsers = ref([
  { id: '1', username: 'student01', avatar: '', postCount: 25, likeCount: 234 },
  { id: '2', username: 'student02', avatar: '', postCount: 18, likeCount: 189 },
  { id: '3', username: 'teacher01', avatar: '', postCount: 12, likeCount: 156 }
])

// 方法
const refreshData = async () => {
  refreshLoading.value = true
  // 模拟刷新数据
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 更新实时数据
  realtimeData.value = realtimeData.value.map(item => ({
    ...item,
    value: item.value + Math.floor(Math.random() * 10) - 5
  }))
  
  refreshLoading.value = false
}

const initCharts = () => {
  // 用户活跃度图表
  if (userChartRef.value) {
    const userChart = echarts.init(userChartRef.value)
    const userOption = {
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: { type: 'value' },
      series: [{
        data: [120, 132, 101, 134, 90, 230, 210],
        type: 'line',
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#409EFF' }
      }]
    }
    userChart.setOption(userOption)
  }

  // 内容统计图表
  if (contentChartRef.value) {
    const contentChart = echarts.init(contentChartRef.value)
    const contentOption = {
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '帖子',
          data: [45, 52, 38, 48, 35, 78, 65],
          type: 'bar',
          itemStyle: { color: '#67C23A' }
        },
        {
          name: '评论',
          data: [67, 89, 56, 72, 45, 98, 87],
          type: 'bar',
          itemStyle: { color: '#E6A23C' }
        }
      ]
    }
    contentChart.setOption(contentOption)
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.realtime-card {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.realtime-card :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.realtime-item {
  text-align: center;
  padding: 16px 0;
}

.realtime-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.realtime-label {
  font-size: 14px;
  opacity: 0.8;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}

.charts-row {
  margin-bottom: 24px;
}

.content-row {
  margin-bottom: 24px;
}

.content-list {
  max-height: 300px;
  overflow-y: auto;
}

.content-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.content-item:last-child {
  border-bottom: none;
}

.rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.content-info {
  flex: 1;
}

.content-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-meta {
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 16px;
}

.content-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .content-meta {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
