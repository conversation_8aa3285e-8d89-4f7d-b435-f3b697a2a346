const userRepository = require('../repositories/user.repository');
const { EncryptionUtil, JwtUtil, redisClient } = require('../utils');
const errorCodes = require('../constants/error-codes');
const { StatusCodes } = require('http-status-codes');
const logger = require('../../config/logger');
const { ErrorMiddleware } = require('../middlewares');

/**
 * 用户服务层
 */
class UserService {
  /**
   * 用户注册
   * @param {Object} userData 用户数据
   * @returns {Promise<Object>} 注册结果
   */
  async register(userData) {
    // 检查用户名是否存在
    if (await userRepository.isUsernameExists(userData.username)) {
      throw ErrorMiddleware.createError(
        '用户名已存在',
        StatusCodes.BAD_REQUEST,
        errorCodes.USERNAME_EXISTS
      );
    }
    
    // 检查手机号是否存在
    if (userData.phone && await userRepository.isPhoneExists(userData.phone)) {
      throw ErrorMiddleware.createError(
        '手机号已存在',
        StatusCodes.BAD_REQUEST,
        errorCodes.PHONE_EXISTS
      );
    }
    
    // 检查邮箱是否存在
    if (userData.email && await userRepository.isEmailExists(userData.email)) {
      throw ErrorMiddleware.createError(
        '邮箱已存在',
        StatusCodes.BAD_REQUEST,
        errorCodes.EMAIL_EXISTS
      );
    }

    // 加密密码
    userData.password = EncryptionUtil.hashPassword(userData.password);

    // 确保昵称存在，如果没有提供昵称，使用用户名作为昵称
    if (!userData.nickname) {
      userData.nickname = userData.username;
    }

    // 创建用户
    const user = await userRepository.create(userData);
    
    // 生成token
    const token = this._generateToken(user);

    // 更新最后登录时间
    await userRepository.updateLastLoginAt(user.id);

    // 返回用户信息和token，格式与前端匹配
    return {
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar || null,
        role: user.role || 'user',
        school: user.school || '',
        department: user.department || ''
      },
      token
    };
  }

  /**
   * 用户登录
   * @param {String} username 用户名/手机号/邮箱
   * @param {String} password 密码
   * @returns {Promise<Object>} 登录结果
   */
  async login(username, password) {
    // 查找用户
    let user = null;
    
    // 尝试使用用户名查找
    user = await userRepository.findByUsername(username, true);
    
    // 尝试使用手机号查找
    if (!user && /^1[3-9]\d{9}$/.test(username)) {
      user = await userRepository.findByPhone(username, true);
    }
    
    // 尝试使用邮箱查找
    if (!user && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(username)) {
      user = await userRepository.findByEmail(username, true);
    }
    
    // 用户不存在
    if (!user) {
      throw ErrorMiddleware.createError(
        '用户不存在',
        StatusCodes.BAD_REQUEST,
        errorCodes.USER_NOT_EXIST
      );
    }
    
    // 检查用户是否被禁用
    if (user.is_disabled) {
      throw ErrorMiddleware.createError(
        '账号已被禁用',
        StatusCodes.FORBIDDEN,
        errorCodes.USER_DISABLED
      );
    }
    
    // 验证密码
    if (!EncryptionUtil.verifyPassword(password, user.password)) {
      throw ErrorMiddleware.createError(
        '密码错误',
        StatusCodes.BAD_REQUEST,
        errorCodes.PASSWORD_ERROR
      );
    }
    
    // 生成token
    const token = this._generateToken(user);
    
    // 更新最后登录时间
    await userRepository.updateLastLoginAt(user.id);
    
    // 返回用户信息和token，格式与前端匹配
    return {
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname || user.username,
        avatar: user.avatar || null,
        role: user.role || 'user',
        school: user.school || '',
        department: user.department || ''
      },
      token
    };
  }

  /**
   * 获取用户信息
   * @param {String} id 用户ID
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(id) {
    const user = await userRepository.findById(id);
    if (!user) {
      throw ErrorMiddleware.createError(
        '用户不存在',
        StatusCodes.NOT_FOUND,
        errorCodes.USER_NOT_EXIST
      );
    }

    // 获取用户统计数据
    const stats = await this.getUserStats(id);

    // 返回包含统计数据的用户信息
    return {
      ...user.toJSON(),
      stats
    };
  }

  /**
   * 获取用户统计数据
   * @param {String} userId 用户ID
   * @returns {Promise<Object>} 统计数据
   */
  async getUserStats(userId) {
    try {
      // 这里需要根据实际的模型关系来查询统计数据
      // 暂时返回模拟数据，后续需要实现真实的统计查询

      // TODO: 实现真实的统计查询
      // const postCount = await Post.count({ where: { author_id: userId } });
      // const likeCount = await Like.count({ where: { user_id: userId } });
      // const favoriteCount = await Favorite.count({ where: { user_id: userId } });
      // const followCount = await Follow.count({ where: { follower_id: userId } });
      // const fansCount = await Follow.count({ where: { following_id: userId } });

      return {
        postCount: 0,
        likeCount: 0,
        favoriteCount: 0,
        followCount: 0,
        fansCount: 0
      };
    } catch (error) {
      logger.error('获取用户统计数据失败:', error);
      return {
        postCount: 0,
        likeCount: 0,
        favoriteCount: 0,
        followCount: 0,
        fansCount: 0
      };
    }
  }

  /**
   * 更新用户信息
   * @param {String} id 用户ID
   * @param {Object} userData 用户数据
   * @returns {Promise<Object>} 更新后的用户信息
   */
  async updateUserInfo(id, userData) {
    // 检查用户是否存在
    const existingUser = await userRepository.findById(id);
    if (!existingUser) {
      throw ErrorMiddleware.createError(
        '用户不存在',
        StatusCodes.NOT_FOUND,
        errorCodes.USER_NOT_EXIST
      );
    }
    
    // 检查用户名是否重复
    if (userData.username && userData.username !== existingUser.username) {
      if (await userRepository.isUsernameExists(userData.username, id)) {
        throw ErrorMiddleware.createError(
          '用户名已存在',
          StatusCodes.BAD_REQUEST,
          errorCodes.USERNAME_EXISTS
        );
      }
    }
    
    // 检查手机号是否重复
    if (userData.phone && userData.phone !== existingUser.phone) {
      if (await userRepository.isPhoneExists(userData.phone, id)) {
        throw ErrorMiddleware.createError(
          '手机号已存在',
          StatusCodes.BAD_REQUEST,
          errorCodes.PHONE_EXISTS
        );
      }
    }
    
    // 检查邮箱是否重复
    if (userData.email && userData.email !== existingUser.email) {
      if (await userRepository.isEmailExists(userData.email, id)) {
        throw ErrorMiddleware.createError(
          '邮箱已存在',
          StatusCodes.BAD_REQUEST,
          errorCodes.EMAIL_EXISTS
        );
      }
    }
    
    // 如果包含密码，需要加密
    if (userData.password) {
      userData.password = EncryptionUtil.hashPassword(userData.password);
    }
    
    // 更新用户信息
    const updatedUser = await userRepository.update(id, userData);
    return updatedUser;
  }

  /**
   * 修改密码
   * @param {String} id 用户ID
   * @param {String} oldPassword 旧密码
   * @param {String} newPassword 新密码
   * @returns {Promise<Boolean>} 是否成功
   */
  async changePassword(id, oldPassword, newPassword) {
    // 查找用户
    const user = await userRepository.findById(id, true);
    if (!user) {
      throw ErrorMiddleware.createError(
        '用户不存在',
        StatusCodes.NOT_FOUND,
        errorCodes.USER_NOT_EXIST
      );
    }
    
    // 验证旧密码
    if (!EncryptionUtil.verifyPassword(oldPassword, user.password)) {
      throw ErrorMiddleware.createError(
        '旧密码错误',
        StatusCodes.BAD_REQUEST,
        errorCodes.PASSWORD_ERROR
      );
    }
    
    // 更新密码
    const hashedPassword = EncryptionUtil.hashPassword(newPassword);
    await userRepository.update(id, { password: hashedPassword });
    
    return true;
  }

  /**
   * 重置密码
   * @param {String} id 用户ID
   * @param {String} newPassword 新密码
   * @returns {Promise<Boolean>} 是否成功
   */
  async resetPassword(id, newPassword) {
    // 查找用户
    const user = await userRepository.findById(id);
    if (!user) {
      throw ErrorMiddleware.createError(
        '用户不存在',
        StatusCodes.NOT_FOUND,
        errorCodes.USER_NOT_EXIST
      );
    }
    
    // 更新密码
    const hashedPassword = EncryptionUtil.hashPassword(newPassword);
    await userRepository.update(id, { password: hashedPassword });
    
    return true;
  }

  /**
   * 查询用户列表
   * @param {Object} options 查询选项
   * @returns {Promise<Object>} 分页结果
   */
  async findUsers(options) {
    return await userRepository.findAll(options);
  }

  /**
   * 禁用或启用用户
   * @param {String} id 用户ID
   * @param {Boolean} isDisabled 是否禁用
   * @returns {Promise<Boolean>} 是否成功
   */
  async setUserStatus(id, isDisabled) {
    // 查找用户
    const user = await userRepository.findById(id);
    if (!user) {
      throw ErrorMiddleware.createError(
        '用户不存在',
        StatusCodes.NOT_FOUND,
        errorCodes.USER_NOT_EXIST
      );
    }
    
    // 更新状态
    return await userRepository.setDisabledStatus(id, isDisabled);
  }

  /**
   * 删除用户
   * @param {String} id 用户ID
   * @returns {Promise<Boolean>} 是否成功
   */
  async deleteUser(id) {
    // 查找用户
    const user = await userRepository.findById(id);
    if (!user) {
      throw ErrorMiddleware.createError(
        '用户不存在',
        StatusCodes.NOT_FOUND,
        errorCodes.USER_NOT_EXIST
      );
    }
    
    // 删除用户
    return await userRepository.delete(id);
  }

  /**
   * 生成JWT令牌
   * @param {Object} user 用户对象
   * @returns {String} JWT令牌
   * @private
   */
  _generateToken(user) {
    return JwtUtil.generateToken({
      id: user.id,
      username: user.username,
      role: user.role
    });
  }

  /**
   * 验证手机验证码
   * @param {String} phone 手机号
   * @param {String} code 验证码
   * @returns {Promise<Boolean>} 是否有效
   */
  async verifyPhoneCode(phone, code) {
    const key = `verify_code:phone:${phone}`;
    const savedCode = await redisClient.get(key);
    
    if (!savedCode) {
      throw ErrorMiddleware.createError(
        '验证码已过期',
        StatusCodes.BAD_REQUEST,
        errorCodes.VERIFY_CODE_EXPIRED
      );
    }
    
    if (savedCode !== code) {
      throw ErrorMiddleware.createError(
        '验证码错误',
        StatusCodes.BAD_REQUEST,
        errorCodes.VERIFY_CODE_ERROR
      );
    }
    
    // 验证成功后删除验证码
    await redisClient.del(key);
    
    return true;
  }

  /**
   * 发送手机验证码
   * @param {String} phone 手机号
   * @returns {Promise<Boolean>} 是否成功
   */
  async sendPhoneCode(phone) {
    // 生成验证码
    const code = EncryptionUtil.generateVerifyCode(6);
    
    // 存储验证码，有效期10分钟
    const key = `verify_code:phone:${phone}`;
    await redisClient.set(key, code, 600);
    
    // TODO: 调用短信发送接口
    logger.info(`向手机号 ${phone} 发送验证码: ${code}`);
    
    return true;
  }
}

module.exports = new UserService(); 