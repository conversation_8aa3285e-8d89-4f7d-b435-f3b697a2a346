/* 用户管理页面样式 */
.users-page {
  padding: 0;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  padding: 8px 0;
}

/* 用户信息单元格 */
.user-info-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.user-meta {
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 辅助文本样式 */
.text-secondary {
  color: #666;
  font-size: 12px;
}

/* 表格样式优化 */
.users-page .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

.users-page .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.users-page .ant-table-cell {
  padding: 12px 16px;
}

/* 操作按钮样式 */
.users-page .ant-btn-text {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.users-page .ant-btn-text:hover {
  background: rgba(0, 0, 0, 0.04);
}

/* 模态框样式 */
.users-page .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.users-page .ant-form-item-label > label {
  font-weight: 600;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-form .ant-space {
    width: 100%;
    justify-content: stretch;
  }
  
  .search-form .ant-input,
  .search-form .ant-select {
    width: 100% !important;
  }
  
  .user-info-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .users-page .ant-table-cell {
    padding: 8px 12px;
  }
}
