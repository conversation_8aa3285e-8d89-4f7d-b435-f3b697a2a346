:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 系统自适应布局相关变量 */
  --sidebar-width: 220px;
  --sidebar-collapsed-width: 64px;
  --header-height: 60px;
  --content-padding: 20px;
  --container-max-width: 1800px;
  --content-padding-right: 20px;

  /* 颜色变量 */
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --online-color: #00dc82;
  
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  
  --border-color: #EBEEF5;
  --bg-color: #F5F7FA;
  --sidebar-bg: #304156;
  --header-bg: #FFFFFF;

  color-scheme: light dark;
  color: var(--text-regular);
  background-color: var(--bg-color);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局样式设置 */

.admin-grid-5 {
  grid-template-columns: repeat(5, 1fr);
}

@media (max-width: 1400px) {
  .admin-grid-5 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .admin-grid-5 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .admin-grid-5 {
    grid-template-columns: 1fr;
  }
}

a {
  font-weight: 500;
  color: var(--primary-color);
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
}

/* 全局自适应容器类 */
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0;
}

/* 全局卡片样式 */
.admin-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  width: 100%;
}

.admin-card-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-card-body {
  padding: 20px;
}

/* 默认网格布局 */
.admin-grid {
  display: grid;
  gap: 20px;
  width: 100%;
}

.admin-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.admin-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.admin-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

/* 响应式网格 */
@media (max-width: 1200px) {
  .admin-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .admin-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .admin-grid-4, .admin-grid-3, .admin-grid-2 {
    grid-template-columns: 1fr;
  }
  
  :root {
    --content-padding: 10px;
    --content-padding-right: 10px;
  }
}

/* 覆盖Element Plus默认样式 */
.el-main {
  padding: var(--content-padding) var(--content-padding-right) var(--content-padding) var(--content-padding) !important;
  background-color: var(--bg-color);
}

#app {
  width: 100%;
  height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
