const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define(
    'User',
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: () => uuidv4(),
        allowNull: false
      },
      username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        validate: {
          len: [3, 50],
          notEmpty: true
        }
      },
      nickname: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: null,
        validate: {
          len: [2, 50]
        }
      },
      password: {
        type: DataTypes.STRING(100),
        allowNull: false
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
        unique: true,
        validate: {
          is: /^1[3-9]\d{9}$/
        }
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true,
        validate: {
          isEmail: true
        }
      },
      avatar: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      background_image: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      role: {
        type: DataTypes.ENUM('student', 'teacher', 'admin'),
        allowNull: false,
        defaultValue: 'student'
      },
      gender: {
        type: DataTypes.ENUM('male', 'female', 'other'),
        allowNull: true
      },
      bio: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      school: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      department: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      is_disabled: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      last_login_at: {
        type: DataTypes.DATE,
        allowNull: true
      }
    },
    {
      tableName: 'users',
      timestamps: true,
      underscored: true,
      paranoid: true, // 启用软删除
      // 隐藏敏感字段
      defaultScope: {
        attributes: { exclude: ['password'] }
      },
      // 定义其他作用域
      scopes: {
        withPassword: {
          attributes: { include: ['password'] }
        },
        basic: {
          attributes: ['id', 'username', 'avatar', 'role']
        }
      }
    }
  );

  // 定义关联关系
  User.associate = models => {
    // 用户与帖子是一对多关系
    User.hasMany(models.Post, {
      foreignKey: 'user_id',
      as: 'posts'
    });

    // 用户与评论是一对多关系
    User.hasMany(models.Comment, {
      foreignKey: 'user_id',
      as: 'comments'
    });

    // 用户与点赞是一对多关系
    User.hasMany(models.Like, {
      foreignKey: 'user_id',
      as: 'likes'
    });

    // 用户与收藏是一对多关系
    User.hasMany(models.Favorite, {
      foreignKey: 'user_id',
      as: 'favorites'
    });

    // 用户与发送消息是一对多关系
    User.hasMany(models.Message, {
      foreignKey: 'sender_id',
      as: 'sentMessages'
    });

    // 用户与接收消息是一对多关系
    User.hasMany(models.Message, {
      foreignKey: 'receiver_id',
      as: 'receivedMessages'
    });

    // 用户与关注关系的关联
    // 用户作为关注者的关注关系
    User.hasMany(models.Follow, {
      foreignKey: 'follower_id',
      as: 'followings'
    });

    // 用户作为被关注者的关注关系
    User.hasMany(models.Follow, {
      foreignKey: 'following_id',
      as: 'followers'
    });

    // 用户通过关注关系关联到其他用户（关注的用户）
    User.belongsToMany(models.User, {
      through: models.Follow,
      foreignKey: 'follower_id',
      otherKey: 'following_id',
      as: 'followingUsers'
    });

    // 用户通过关注关系关联到其他用户（粉丝用户）
    User.belongsToMany(models.User, {
      through: models.Follow,
      foreignKey: 'following_id',
      otherKey: 'follower_id',
      as: 'followerUsers'
    });
  };

  return User;
}; 