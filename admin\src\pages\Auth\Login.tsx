import React, { useState } from 'react'
import { Form, Input, But<PERSON>, Card, message, Checkbox } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '../../stores/auth'
import { authService } from '../../services/auth'
import type { LoginForm } from '../../types'
import './Login.css'

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()
  const { login } = useAuthStore()

  const handleSubmit = async (values: LoginForm & { remember?: boolean }) => {
    setLoading(true)
    try {
      const response = await authService.login({
        username: values.username,
        password: values.password,
      })

      // 保存用户信息和token
      login(response.user, response.token)
      
      message.success('登录成功')
      navigate('/', { replace: true })
    } catch (error: any) {
      message.error(error.message || '登录失败，请检查用户名和密码')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay"></div>
      </div>
      
      <Card className="login-card" title={null} bordered={false}>
        <div className="login-header">
          <div className="login-logo">
            <img src="/logo.svg" alt="Logo" />
          </div>
          <h1 className="login-title">校园墙管理系统</h1>
          <p className="login-subtitle">管理员登录</p>
        </div>

        <Form
          name="login"
          size="large"
          onFinish={handleSubmit}
          autoComplete="off"
          className="login-form"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <div className="login-options">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>记住我</Checkbox>
              </Form.Item>
              <a className="forgot-password" href="#forgot">
                忘记密码？
              </a>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="login-button"
              block
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div className="login-footer">
          <p>© 2024 校园墙管理系统. All rights reserved.</p>
        </div>
      </Card>
    </div>
  )
}

export default Login
