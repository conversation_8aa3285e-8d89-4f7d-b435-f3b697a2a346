import React, { useState, useEffect } from 'react'
import {
  Card,
  Tabs,
  Table,
  Button,
  Input,
  Form,
  Modal,
  Switch,
  InputNumber,
  Space,
  message,
  Popconfirm,
  Tag,
  Tooltip,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DragOutlined,
  SettingOutlined,
  AppstoreOutlined,
  TagsOutlined,
} from '@ant-design/icons'
import type { Category, Topic } from '@/types'
import type { ColumnsType } from 'antd/es/table'
import './index.css'

const { TabPane } = Tabs

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('categories')

  // 分类管理状态
  const [categories, setCategories] = useState<Category[]>([])
  const [categoryModalVisible, setCategoryModalVisible] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [categoryForm] = Form.useForm()

  // 话题管理状态
  const [topics, setTopics] = useState<Topic[]>([])
  const [topicModalVisible, setTopicModalVisible] = useState(false)
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null)
  const [topicForm] = Form.useForm()

  // 系统配置状态
  const [systemConfig, setSystemConfig] = useState({
    siteName: '校园墙',
    siteDescription: '校园社交平台',
    allowRegister: true,
    requireEmailVerify: false,
    maxPostLength: 1000,
    maxImageCount: 9,
    enableComment: true,
    enableLike: true,
    enableFavorite: true,
  })
  const [configForm] = Form.useForm()

  // 模拟分类数据
  const mockCategories: Category[] = [
    {
      id: 1,
      name: '校园生活',
      description: '分享校园日常生活',
      icon: '🏫',
      sort: 1,
      isActive: true,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    {
      id: 2,
      name: '学习交流',
      description: '学习经验分享',
      icon: '📚',
      sort: 2,
      isActive: true,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
    {
      id: 3,
      name: '活动通知',
      description: '校园活动通知',
      icon: '📢',
      sort: 3,
      isActive: true,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
    },
  ]

  // 模拟话题数据
  const mockTopics: Topic[] = [
    {
      id: 1,
      name: '樱花季',
      description: '春天樱花相关话题',
      postCount: 25,
      isHot: true,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T00:00:00.000Z',
    },
    {
      id: 2,
      name: '编程学习',
      description: '编程技术学习交流',
      postCount: 48,
      isHot: true,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T00:00:00.000Z',
    },
    {
      id: 3,
      name: '社团活动',
      description: '各类社团活动',
      postCount: 12,
      isHot: false,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T00:00:00.000Z',
    },
  ]

  // 分类表格列定义
  const categoryColumns: ColumnsType<Category> = [
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: 80,
      render: (sort: number) => (
        <div className="sort-handle">
          <DragOutlined />
          <span>{sort}</span>
        </div>
      ),
    },
    {
      title: '分类信息',
      key: 'categoryInfo',
      render: (_, record) => (
        <div className="category-info">
          <span className="category-icon">{record.icon}</span>
          <div className="category-details">
            <div className="category-name">{record.name}</div>
            <div className="category-desc">{record.description}</div>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditCategory(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除该分类吗？"
              onConfirm={() => handleDeleteCategory(record)}
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 话题表格列定义
  const topicColumns: ColumnsType<Topic> = [
    {
      title: '话题名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <div className="topic-info">
          <span className="topic-name">{name}</span>
          {record.isHot && <Tag color="red" size="small">热门</Tag>}
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '帖子数量',
      dataIndex: 'postCount',
      key: 'postCount',
      width: 100,
      render: (count: number) => <span>{count}</span>,
    },
    {
      title: '是否热门',
      dataIndex: 'isHot',
      key: 'isHot',
      width: 100,
      render: (isHot: boolean) => (
        <Tag color={isHot ? 'red' : 'default'}>
          {isHot ? '热门' : '普通'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditTopic(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除该话题吗？"
              onConfirm={() => handleDeleteTopic(record)}
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 分类管理方法
  const handleAddCategory = () => {
    setEditingCategory(null)
    categoryForm.resetFields()
    setCategoryModalVisible(true)
  }

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
    categoryForm.setFieldsValue(category)
    setCategoryModalVisible(true)
  }

  const handleSaveCategory = async () => {
    try {
      const values = await categoryForm.validateFields()
      message.success(editingCategory ? '分类更新成功' : '分类创建成功')
      setCategoryModalVisible(false)
      loadCategories()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleDeleteCategory = (category: Category) => {
    message.success('分类删除成功')
    loadCategories()
  }

  // 话题管理方法
  const handleAddTopic = () => {
    setEditingTopic(null)
    topicForm.resetFields()
    setTopicModalVisible(true)
  }

  const handleEditTopic = (topic: Topic) => {
    setEditingTopic(topic)
    topicForm.setFieldsValue(topic)
    setTopicModalVisible(true)
  }

  const handleSaveTopic = async () => {
    try {
      const values = await topicForm.validateFields()
      message.success(editingTopic ? '话题更新成功' : '话题创建成功')
      setTopicModalVisible(false)
      loadTopics()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleDeleteTopic = (topic: Topic) => {
    message.success('话题删除成功')
    loadTopics()
  }

  // 系统配置方法
  const handleSaveConfig = async () => {
    try {
      const values = await configForm.validateFields()
      setSystemConfig(values)
      message.success('系统配置保存成功')
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 加载数据
  const loadCategories = () => {
    setCategories(mockCategories)
  }

  const loadTopics = () => {
    setTopics(mockTopics)
  }

  useEffect(() => {
    loadCategories()
    loadTopics()
    configForm.setFieldsValue(systemConfig)
  }, [])

  return (
    <div className="settings-page">
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 分类管理 */}
          <TabPane
            tab={
              <span>
                <AppstoreOutlined />
                分类管理
              </span>
            }
            key="categories"
          >
            <div className="tab-content">
              <div className="tab-header">
                <h3>分类管理</h3>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddCategory}
                >
                  新增分类
                </Button>
              </div>
              <Table
                columns={categoryColumns}
                dataSource={categories}
                rowKey="id"
                pagination={false}
              />
            </div>
          </TabPane>

          {/* 话题管理 */}
          <TabPane
            tab={
              <span>
                <TagsOutlined />
                话题管理
              </span>
            }
            key="topics"
          >
            <div className="tab-content">
              <div className="tab-header">
                <h3>话题管理</h3>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddTopic}
                >
                  新增话题
                </Button>
              </div>
              <Table
                columns={topicColumns}
                dataSource={topics}
                rowKey="id"
                pagination={false}
              />
            </div>
          </TabPane>

          {/* 系统配置 */}
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                系统配置
              </span>
            }
            key="config"
          >
            <div className="tab-content">
              <div className="tab-header">
                <h3>系统配置</h3>
                <Button type="primary" onClick={handleSaveConfig}>
                  保存配置
                </Button>
              </div>
              <Form
                form={configForm}
                layout="vertical"
                initialValues={systemConfig}
                className="config-form"
              >
                <div className="config-section">
                  <h4>基础设置</h4>
                  <Form.Item
                    name="siteName"
                    label="站点名称"
                    rules={[{ required: true, message: '请输入站点名称' }]}
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item
                    name="siteDescription"
                    label="站点描述"
                  >
                    <Input.TextArea rows={3} />
                  </Form.Item>
                </div>

                <div className="config-section">
                  <h4>用户设置</h4>
                  <Form.Item
                    name="allowRegister"
                    label="允许用户注册"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    name="requireEmailVerify"
                    label="需要邮箱验证"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <div className="config-section">
                  <h4>内容设置</h4>
                  <Form.Item
                    name="maxPostLength"
                    label="帖子最大长度"
                    rules={[{ required: true, message: '请输入帖子最大长度' }]}
                  >
                    <InputNumber min={100} max={5000} addonAfter="字符" />
                  </Form.Item>
                  <Form.Item
                    name="maxImageCount"
                    label="最大图片数量"
                    rules={[{ required: true, message: '请输入最大图片数量' }]}
                  >
                    <InputNumber min={1} max={20} addonAfter="张" />
                  </Form.Item>
                </div>

                <div className="config-section">
                  <h4>功能开关</h4>
                  <Form.Item
                    name="enableComment"
                    label="启用评论功能"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    name="enableLike"
                    label="启用点赞功能"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    name="enableFavorite"
                    label="启用收藏功能"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>
              </Form>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 分类编辑模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '新增分类'}
        open={categoryModalVisible}
        onOk={handleSaveCategory}
        onCancel={() => setCategoryModalVisible(false)}
        width={500}
      >
        <Form
          form={categoryForm}
          layout="vertical"
          initialValues={editingCategory || {}}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="分类描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="icon"
            label="分类图标"
            rules={[{ required: true, message: '请输入分类图标' }]}
          >
            <Input placeholder="请输入emoji图标" />
          </Form.Item>
          <Form.Item
            name="sort"
            label="排序"
            rules={[{ required: true, message: '请输入排序' }]}
          >
            <InputNumber min={1} />
          </Form.Item>
          <Form.Item
            name="isActive"
            label="是否启用"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* 话题编辑模态框 */}
      <Modal
        title={editingTopic ? '编辑话题' : '新增话题'}
        open={topicModalVisible}
        onOk={handleSaveTopic}
        onCancel={() => setTopicModalVisible(false)}
        width={500}
      >
        <Form
          form={topicForm}
          layout="vertical"
          initialValues={editingTopic || {}}
        >
          <Form.Item
            name="name"
            label="话题名称"
            rules={[{ required: true, message: '请输入话题名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="话题描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="isHot"
            label="是否热门"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Settings
