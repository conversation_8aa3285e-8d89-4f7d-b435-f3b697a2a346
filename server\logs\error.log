{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"campus-wall-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:22:20)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","syscall":"listen","timestamp":"2025-05-31 03:20:06"}
{"body":{},"error":"Not Found - /favicon.ico","level":"error","message":"[GET] /favicon.ico","params":{},"query":{},"service":"campus-wall-api","stack":"Error: Not Found - /favicon.ico\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\logger.middleware.js:53:7\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9","timestamp":"2025-05-31 03:20:29"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"campus-wall-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:22:20)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","syscall":"listen","timestamp":"2025-05-31 03:23:32"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"campus-wall-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:22:20)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","syscall":"listen","timestamp":"2025-05-31 03:24:14"}
{"level":"error","message":"Uncaught Exception: jwt.verify is not a function","service":"campus-wall-api","stack":"TypeError: jwt.verify is not a function\n    at WebSocketService._handleConnection (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\utils\\websocket.js:46:9)\n    at WebSocketServer.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\utils\\websocket.js:23:12)\n    at WebSocketServer.emit (node:events:518:28)\n    at WebSocketServer.completeUpgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:436:5)\n    at WebSocketServer.handleUpgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:344:10)\n    at Server.upgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:119:16)\n    at Server.emit (node:events:518:28)\n    at onParserExecuteCommon (node:_http_server:950:14)\n    at onParserExecute (node:_http_server:852:3)","timestamp":"2025-05-31 03:35:15"}
{"level":"error","message":"Uncaught Exception: jwt.verify is not a function","service":"campus-wall-api","stack":"TypeError: jwt.verify is not a function\n    at WebSocketService._handleConnection (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\utils\\websocket.js:46:9)\n    at WebSocketServer.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\utils\\websocket.js:23:12)\n    at WebSocketServer.emit (node:events:518:28)\n    at WebSocketServer.completeUpgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:436:5)\n    at WebSocketServer.handleUpgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:344:10)\n    at Server.upgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:119:16)\n    at Server.emit (node:events:518:28)\n    at onParserExecuteCommon (node:_http_server:950:14)\n    at onParserExecute (node:_http_server:852:3)","timestamp":"2025-05-31 03:38:59"}
{"level":"error","message":"Uncaught Exception: jwt.verify is not a function","service":"campus-wall-api","stack":"TypeError: jwt.verify is not a function\n    at WebSocketService._handleConnection (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\utils\\websocket.js:46:9)\n    at WebSocketServer.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\utils\\websocket.js:23:12)\n    at WebSocketServer.emit (node:events:518:28)\n    at WebSocketServer.completeUpgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:436:5)\n    at WebSocketServer.handleUpgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:344:10)\n    at Server.upgrade (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ws\\lib\\websocket-server.js:119:16)\n    at Server.emit (node:events:518:28)\n    at onParserExecuteCommon (node:_http_server:950:14)\n    at onParserExecute (node:_http_server:852:3)","timestamp":"2025-05-31 03:41:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:45:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:46:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:47:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:48:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:49:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:50:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:51:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:52:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:53:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:54:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:55:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:56:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:57:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:58:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 03:59:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:00:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:01:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:02:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:03:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:04:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:05:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:06:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:07:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:08:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:09:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:10:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:11:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:12:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:13:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:14:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:15:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:16:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:17:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:18:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:19:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:20:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:21:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:22:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:23:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:24:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:25:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:26:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:27:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:28:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:29:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:30:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:31:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:32:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:33:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:34:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:35:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:36:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:13"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:17"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:25"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:37:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:38:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:39:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:40:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:00"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:04"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:16"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:20"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:28"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:32"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:36"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:40"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:44"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:48"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:52"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:56"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:41:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:30"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:34"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:38"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:42"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:46"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:50"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:42:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:29"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:33"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:37"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:41"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:45"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:49"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:53"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:43:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:08"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:44:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:45:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:46:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:55"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:47:59"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:03"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:07"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:11"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:19"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:23"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:27"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:31"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:35"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:39"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:43"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:47"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:51"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:54"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:48:58"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:49:02"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:49:06"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:49:10"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:49:14"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:49:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:49:22"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 04:49:26"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:13:57"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:01"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:05"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:09"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:12"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:15"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:18"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:21"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:24"}
{"level":"error","message":"WebSocket认证失败: 无效的token","service":"campus-wall-api","timestamp":"2025-05-31 05:14:27"}
{"body":{"nickname":"12345","password":"123456","username":"12345678900"},"error":"未找到资源 - /api/auth/register","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error: 未找到资源 - /api/auth/register\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-05-31 05:22:19"}
{"body":{"nickname":"12345","password":"123456","username":"12345678900"},"error":"未找到资源 - /api/auth/register","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error: 未找到资源 - /api/auth/register\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-05-31 05:22:30"}
{"body":{"nickname":"12345","password":"123456","username":"12345678900"},"error":"未找到资源 - /api/auth/register","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error: 未找到资源 - /api/auth/register\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-05-31 05:42:33"}
{"level":"error","message":"Unable to connect to the database: Unknown database 'campus_community'","name":"SequelizeConnectionError","original":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'campus_community'","sqlState":"42000"},"parent":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'campus_community'","sqlState":"42000"},"service":"campus-wall-api","stack":"SequelizeConnectionError: Unknown database 'campus_community'\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)\n    at async C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:174:32\n    at async ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:197:7)\n    at async C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:305:26\n    at async Sequelize.authenticate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:457:5)","timestamp":"2025-05-31 05:46:20"}
{"level":"error","message":"Unable to connect to the database: Unknown database 'campus_community'","name":"SequelizeConnectionError","original":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'campus_community'","sqlState":"42000"},"parent":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'campus_community'","sqlState":"42000"},"service":"campus-wall-api","stack":"SequelizeConnectionError: Unknown database 'campus_community'\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)\n    at async C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:174:32\n    at async ConnectionManager.getConnection (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:197:7)\n    at async C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:305:26\n    at async Sequelize.authenticate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:457:5)","timestamp":"2025-05-31 05:47:16"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:14:55"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:15:05"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:15:15"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:15:25"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:15:36"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:15:49"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:16:00"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:16:10"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:16:20"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:16:30"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:16:40"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:16:51"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:01"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:03"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:11"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:13"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:22"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:32"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:43"}
{"code":"ETIMEDOUT","errorno":"ETIMEDOUT","level":"error","message":"Redis connection error: connect ETIMEDOUT","service":"campus-wall-api","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","syscall":"connect","timestamp":"2025-05-31 13:17:54"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"campus-wall-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:22:20)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","syscall":"listen","timestamp":"2025-05-31 13:19:26"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"campus-wall-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:22:20)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","syscall":"listen","timestamp":"2025-05-31 13:20:10"}
{"body":{},"error":"Converting circular structure to JSON\n    --> starting at object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    |     property 'parent' -> object with constructor 'Object'\n    --- property 'include' closes the circle","level":"error","message":"[GET] /api/posts?page=1&pageSize=10&category=&sort=recommend","params":{},"query":{"category":"","page":"1","pageSize":"10","sort":"recommend"},"service":"campus-wall-api","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    |     property 'parent' -> object with constructor 'Object'\n    --- property 'include' closes the circle\n    at JSON.stringify (<anonymous>)\n    at Printf.template (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\config\\logger.js:23:81)\n    at Printf.transform (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\logform\\printf.js:11:26)\n    at Format.transform (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\logform\\combine.js:20:24)\n    at Console._write (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\modern.js:91:33)\n    at doWrite (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)\n    at writeOrBuffer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:381:5)\n    at Writable.write (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:302:11)\n    at DerivedLogger.ondata (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston\\node_modules\\readable-stream\\lib\\_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:530:35)","timestamp":"2025-05-31 13:23:30"}
{"contentLength":"320","duration":"274ms","level":"error","message":"Request completed: GET /api/posts?page=1&pageSize=10&category=&sort=recommend","method":"GET","requestId":"1748669010546-7ag5f8ti","service":"campus-wall-api","statusCode":500,"timestamp":"2025-05-31 13:23:30","url":"/api/posts?page=1&pageSize=10&category=&sort=recommend"}
{"body":{"nickname":"某某","password":"123456","username":"12345678900"},"error":"Unknown column 'is_disabled' in 'field list'","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.insert (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async User.create (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async UserRepository.create (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\user.repository.js:58:12)\n    at async UserService.register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:54:18)\n    at async register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:27:22)","timestamp":"2025-05-31 13:24:28"}
{"contentLength":"108","duration":"264ms","level":"error","message":"Request completed: POST /api/auth/register","method":"POST","requestId":"1748669068174-xrvmvfv5","service":"campus-wall-api","statusCode":500,"timestamp":"2025-05-31 13:24:28","url":"/api/auth/register"}
{"body":{"nickname":"某某","password":"123456","username":"12345678900"},"error":"Data truncated for column 'id' at row 1","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.insert (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async User.create (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async UserRepository.create (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\user.repository.js:58:12)\n    at async UserService.register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:54:18)\n    at async register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:27:22)","timestamp":"2025-05-31 13:30:29"}
{"contentLength":"103","duration":"237ms","level":"error","message":"Request completed: POST /api/auth/register","method":"POST","requestId":"1748669429242-p3oolmlz","service":"campus-wall-api","statusCode":500,"timestamp":"2025-05-31 13:30:29","url":"/api/auth/register"}
{"body":{"nickname":"某某","password":"123456","username":"12345678900"},"error":"Field 'email' doesn't have a default value","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.insert (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async User.create (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async UserRepository.create (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\user.repository.js:58:12)\n    at async UserService.register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:54:18)\n    at async register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:27:22)","timestamp":"2025-05-31 13:36:52"}
{"contentLength":"106","duration":"185ms","level":"error","message":"Request completed: POST /api/auth/register","method":"POST","requestId":"1748669812597-8f93r2bk","service":"campus-wall-api","statusCode":500,"timestamp":"2025-05-31 13:36:52","url":"/api/auth/register"}
{"body":{"nickname":"12345","password":"123456","username":"12345678900"},"error":"用户名已存在","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error: 用户名已存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:20:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:27:22)","timestamp":"2025-05-31 13:51:19"}
{"body":{"nickname":"某某","password":"123456","username":"12345678900"},"error":"用户名已存在","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error: 用户名已存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:20:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:27:22)","timestamp":"2025-05-31 13:52:04"}
{"body":{"nickname":"测试用户","password":"123456","username":"12345678900"},"error":"用户名已存在","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error: 用户名已存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:20:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:27:22)","timestamp":"2025-05-31 13:54:13"}
{"body":{"nickname":"bbbb","password":"123456","username":"12345678900"},"error":"用户名已存在","level":"error","message":"[POST] /api/auth/register","params":{},"query":{},"service":"campus-wall-api","stack":"Error: 用户名已存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:20:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:27:22)","timestamp":"2025-05-31 13:54:21"}
{"body":{},"error":"Converting circular structure to JSON\n    --> starting at object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    |     property 'parent' -> object with constructor 'Object'\n    --- property 'include' closes the circle","level":"error","message":"[GET] /api/posts?page=1&pageSize=10&category=&sort=recommend","params":{},"query":{"category":"","page":"1","pageSize":"10","sort":"recommend"},"service":"campus-wall-api","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    |     property 'parent' -> object with constructor 'Object'\n    --- property 'include' closes the circle\n    at JSON.stringify (<anonymous>)\n    at Printf.template (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\config\\logger.js:23:81)\n    at Printf.transform (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\logform\\printf.js:11:26)\n    at Format.transform (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\logform\\combine.js:20:24)\n    at Console._write (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\modern.js:91:33)\n    at doWrite (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)\n    at writeOrBuffer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:381:5)\n    at Writable.write (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:302:11)\n    at DerivedLogger.ondata (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston\\node_modules\\readable-stream\\lib\\_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:530:35)","timestamp":"2025-05-31 13:54:33"}
{"contentLength":"320","duration":"563ms","level":"error","message":"Request completed: GET /api/posts?page=1&pageSize=10&category=&sort=recommend","method":"GET","requestId":"1748670873132-x6jjhq1i","service":"campus-wall-api","statusCode":500,"timestamp":"2025-05-31 13:54:33","url":"/api/posts?page=1&pageSize=10&category=&sort=recommend"}
{"body":{},"error":"Converting circular structure to JSON\n    --> starting at object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    |     property 'parent' -> object with constructor 'Object'\n    --- property 'include' closes the circle","level":"error","message":"[GET] /api/posts?page=1&pageSize=10&category=&sort=recommend","params":{},"query":{"category":"","page":"1","pageSize":"10","sort":"recommend"},"service":"campus-wall-api","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    |     property 'parent' -> object with constructor 'Object'\n    --- property 'include' closes the circle\n    at JSON.stringify (<anonymous>)\n    at Printf.template (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\config\\logger.js:23:81)\n    at Printf.transform (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\logform\\printf.js:11:26)\n    at Format.transform (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\logform\\combine.js:20:24)\n    at Console._write (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\modern.js:91:33)\n    at doWrite (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)\n    at writeOrBuffer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:381:5)\n    at Writable.write (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:302:11)\n    at DerivedLogger.ondata (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\winston\\node_modules\\readable-stream\\lib\\_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:530:35)","timestamp":"2025-05-31 13:55:40"}
{"contentLength":"320","duration":"345ms","level":"error","message":"Request completed: GET /api/posts?page=1&pageSize=10&category=&sort=recommend","method":"GET","requestId":"1748670940135-zrr1mfi7","service":"campus-wall-api","statusCode":500,"timestamp":"2025-05-31 13:55:40","url":"/api/posts?page=1&pageSize=10&category=&sort=recommend"}
2025-05-31 14:00:05 error: [GET] /api/posts?page=1&pageSize=10&category=&sort=recommend {
  "service": "campus-wall-api",
  "error": "Table 'campus_community.posts' doesn't exist",
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Post.aggregate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Post.count (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)\n    at async Post.findAndCountAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1322:27)\n    at async PostRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\post.repository.js:293:29)\n    at async PostService.getPosts (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:310:19)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "category": "",
    "sort": "recommend"
  }
}
2025-05-31 14:00:05 error: Request completed: GET /api/posts?page=1&pageSize=10&category=&sort=recommend {
  "service": "campus-wall-api",
  "requestId": "1748671205438-wu5u6kps",
  "method": "GET",
  "url": "/api/posts?page=1&pageSize=10&category=&sort=recommend",
  "statusCode": 500,
  "duration": "30ms",
  "contentLength": "108"
}
2025-05-31 14:07:04 error: 启动服务器失败: Validation error {
  "service": "campus-wall-api",
  "name": "SequelizeUniqueConstraintError",
  "errors": [
    {
      "message": "name must be unique",
      "type": "unique violation",
      "path": "name",
      "value": "校园生活",
      "origin": "DB",
      "instance": null,
      "validatorKey": "not_unique",
      "validatorName": null,
      "validatorArgs": []
    }
  ],
  "parent": "[Circular]",
  "original": {
    "code": "ER_DUP_ENTRY",
    "errno": 1062,
    "sqlState": "23000",
    "sqlMessage": "Duplicate entry '校园生活' for key 'categories.name'",
    "sql": "ALTER TABLE `categories` CHANGE `name` `name` VARCHAR(50) NOT NULL UNIQUE;"
  },
  "fields": {
    "name": "校园生活"
  },
  "sql": "ALTER TABLE `categories` CHANGE `name` `name` VARCHAR(50) NOT NULL UNIQUE;",
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Category.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:27:5)"
}
2025-05-31 14:47:42 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 14:48:13 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 14:48:29 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 14:49:11 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 14:49:28 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 14:53:45 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3001 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3001,
  "stack": "Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 14:54:04 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3001 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3001,
  "stack": "Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 14:57:13 error: [GET] /api/users/me {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:156:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)",
  "body": {},
  "params": {},
  "query": {}
}
2025-05-31 14:57:13 error: [GET] /api/users/me {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:156:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)",
  "body": {},
  "params": {},
  "query": {}
}
2025-05-31 14:57:27 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "12345",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-05-31 14:57:31 error: [GET] /api/users/me {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:156:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)",
  "body": {},
  "params": {},
  "query": {}
}
2025-05-31 14:57:33 error: [GET] /api/users/me {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:156:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)",
  "body": {},
  "params": {},
  "query": {}
}
2025-05-31 14:57:33 error: [GET] /api/users/me {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:156:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)",
  "body": {},
  "params": {},
  "query": {}
}
2025-05-31 14:57:47 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "测试",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-05-31 14:57:58 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "bbbb",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-05-31 14:58:07 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "12345678900",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-05-31 14:58:11 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "12345678901",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-05-31 15:17:59 error: [GET] /api/user/posts?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "error": "未找到资源 - /api/user/posts?page=1&pageSize=10&type=published",
  "stack": "Error: 未找到资源 - /api/user/posts?page=1&pageSize=10&type=published\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "type": "published"
  }
}
2025-05-31 15:18:10 error: [GET] /api/user/posts?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "error": "未找到资源 - /api/user/posts?page=1&pageSize=10&type=published",
  "stack": "Error: 未找到资源 - /api/user/posts?page=1&pageSize=10&type=published\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "type": "published"
  }
}
2025-05-31 15:20:19 error: [GET] /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "error": "未找到资源 - /api/posts/user/me?page=1&pageSize=10&type=published",
  "stack": "Error: 未找到资源 - /api/posts/user/me?page=1&pageSize=10&type=published\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "type": "published"
  }
}
2025-05-31 15:34:50 error: [GET] /favicon.ico {
  "service": "campus-wall-api",
  "error": "未找到资源 - /favicon.ico",
  "stack": "Error: 未找到资源 - /favicon.ico\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {}
}
2025-05-31 16:20:53 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 16:21:54 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-05-31 23:06:52 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "分类不存在",
  "stack": "Error: 分类不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:46:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "E其eqEQe",
    "content": "adDD",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:18:54 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "分类不存在",
  "stack": "Error: 分类不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:46:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "wddwdwfwfw",
    "content": "败给",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:28:47 error: 启动服务器失败: Too many keys specified; max 64 keys allowed {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_TOO_MANY_KEYS",
    "errno": 1069,
    "sqlState": "42000",
    "sqlMessage": "Too many keys specified; max 64 keys allowed",
    "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;"
  },
  "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:27:5)"
}
2025-05-31 23:28:48 error: 启动服务器失败: Too many keys specified; max 64 keys allowed {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_TOO_MANY_KEYS",
    "errno": 1069,
    "sqlState": "42000",
    "sqlMessage": "Too many keys specified; max 64 keys allowed",
    "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;"
  },
  "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:27:5)"
}
2025-05-31 23:29:14 error: 启动服务器失败: Too many keys specified; max 64 keys allowed {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_TOO_MANY_KEYS",
    "errno": 1069,
    "sqlState": "42000",
    "sqlMessage": "Too many keys specified; max 64 keys allowed",
    "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;"
  },
  "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:27:5)"
}
2025-05-31 23:30:20 error: 启动服务器失败: Too many keys specified; max 64 keys allowed {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_TOO_MANY_KEYS",
    "errno": 1069,
    "sqlState": "42000",
    "sqlMessage": "Too many keys specified; max 64 keys allowed",
    "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;"
  },
  "sql": "ALTER TABLE `users` CHANGE `username` `username` VARCHAR(50) NOT NULL UNIQUE;",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:27:5)"
}
2025-05-31 23:37:49 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "qewrtey",
    "content": "wsqs",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:37:49 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1748705869082-vwswz4fa",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "24ms",
  "contentLength": "123"
}
2025-05-31 23:38:56 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "qewrtey",
    "content": "wsqs",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:38:56 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1748705936688-2lcasuov",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "10ms",
  "contentLength": "123"
}
2025-05-31 23:38:57 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "qewrtey",
    "content": "wsqs",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:38:57 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1748705937449-cd0d7qhr",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "12ms",
  "contentLength": "123"
}
2025-05-31 23:38:57 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "qewrtey",
    "content": "wsqs",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:38:57 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1748705937666-2ghhlwpf",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "9ms",
  "contentLength": "123"
}
2025-05-31 23:38:57 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "qewrtey",
    "content": "wsqs",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:38:57 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1748705937851-qk4zermn",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "8ms",
  "contentLength": "123"
}
2025-05-31 23:43:46 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "qSDAEFSAS",
    "content": "Sadesgdrfa",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:43:46 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1748706226855-1ow24b3t",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "8ms",
  "contentLength": "123"
}
2025-05-31 23:45:35 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "qSDAEFSAS",
    "content": "Sadesgdrfa",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-05-31 23:45:35 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1748706335976-n3o6l4d9",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "8ms",
  "contentLength": "123"
}
2025-06-04 16:52:21 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 16:52:31 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 16:52:41 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 16:52:50 error: Redis connection error: connect ECONNABORTED ***************:6379 {
  "service": "campus-wall-api",
  "errno": -4079,
  "code": "ECONNABORTED",
  "syscall": "connect",
  "address": "***************",
  "port": 6379,
  "stack": "Error: connect ECONNABORTED ***************:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)"
}
2025-06-04 16:53:00 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 19:57:34 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 19:57:44 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 19:57:49 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-06-04 19:57:54 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 19:58:04 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-04 20:00:06 error: [POST] /api/posts {
  "service": "campus-wall-api",
  "error": "Cannot read properties of undefined (reading 'transaction')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'transaction')\n    at PostService.createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:56:56)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createPost (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:38:20)",
  "body": {
    "title": "dawscsefesff",
    "content": "adff",
    "category_id": 1
  },
  "params": {},
  "query": {}
}
2025-06-04 20:00:06 error: Request completed: POST /api/posts {
  "service": "campus-wall-api",
  "requestId": "1749038406713-nqcacb3y",
  "method": "POST",
  "url": "/api/posts",
  "statusCode": 500,
  "duration": "21ms",
  "contentLength": "123"
}
2025-06-07 18:36:45 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-07 18:36:55 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-07 18:37:05 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-07 18:37:15 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-07 19:54:37 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 19:54:37 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297277988-x4ceqcw1",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "5ms",
  "contentLength": "122"
}
2025-06-07 19:54:52 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 19:54:52 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297292507-unpzmypo",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "3ms",
  "contentLength": "122"
}
2025-06-07 19:55:14 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 19:55:14 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297314704-cbr83zg2",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "7ms",
  "contentLength": "122"
}
2025-06-07 19:56:07 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 19:56:07 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297367570-n0zrhft3",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "3ms",
  "contentLength": "122"
}
2025-06-07 19:56:17 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 19:56:17 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297377500-jzzr7l9d",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "3ms",
  "contentLength": "122"
}
2025-06-07 19:59:19 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 19:59:19 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297559540-o4tx1bvj",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "4ms",
  "contentLength": "122"
}
2025-06-07 19:59:21 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 19:59:21 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297561995-l40m97gm",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "3ms",
  "contentLength": "122"
}
2025-06-07 20:01:43 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 20:01:43 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297703995-j4mi4o53",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "2ms",
  "contentLength": "122"
}
2025-06-07 20:01:44 error: [GET] /api/categories {
  "service": "campus-wall-api",
  "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON",
  "stack": "SyntaxError: Unexpected token 'o', \"[object Obj\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at CategoryRepository.findAll (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\category.repository.js:94:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CategoryService.getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\category.service.js:111:12)\n    at async getAllCategories (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\category.controller.js:106:26)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-07 20:01:44 error: Request completed: GET /api/categories {
  "service": "campus-wall-api",
  "requestId": "1749297704660-gn1h2u32",
  "method": "GET",
  "url": "/api/categories",
  "statusCode": 500,
  "duration": "3ms",
  "contentLength": "122"
}
2025-06-07 20:04:22 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-06-07 20:17:58 error: [GET] /posts?page=1&pageSize=10&categoryId=&sort=recommend {
  "service": "campus-wall-api",
  "error": "未找到资源 - /posts?page=1&pageSize=10&categoryId=&sort=recommend",
  "stack": "Error: 未找到资源 - /posts?page=1&pageSize=10&categoryId=&sort=recommend\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "categoryId": "",
    "sort": "recommend"
  }
}
2025-06-07 20:30:18 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:30:46 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:31:23 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:31:57 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:32:20 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:32:47 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:33:05 error: [GET] /api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sortType=latest {
  "service": "campus-wall-api",
  "error": "\"[object Object]\" is not valid JSON",
  "stack": "SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at PostRepository.findById (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\post.repository.js:36:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostService.getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:364:18)\n    at async getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:223:22)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "sortType": "latest"
  }
}
2025-06-07 20:33:05 error: Request completed: GET /api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sortType=latest {
  "service": "campus-wall-api",
  "requestId": "1749299585533-7b1044lh",
  "method": "GET",
  "url": "/api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sortType=latest",
  "statusCode": 500,
  "duration": "3ms",
  "contentLength": "101"
}
2025-06-07 20:33:05 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:33:08 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:33:10 error: [GET] /api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sortType=latest {
  "service": "campus-wall-api",
  "error": "\"[object Object]\" is not valid JSON",
  "stack": "SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at PostRepository.findById (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\post.repository.js:36:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostService.getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:364:18)\n    at async getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:223:22)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "sortType": "latest"
  }
}
2025-06-07 20:33:10 error: Request completed: GET /api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sortType=latest {
  "service": "campus-wall-api",
  "requestId": "1749299590981-m446oish",
  "method": "GET",
  "url": "/api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sortType=latest",
  "statusCode": 500,
  "duration": "3ms",
  "contentLength": "101"
}
2025-06-07 20:33:10 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 20:37:19 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "8507e97c-a576-4ec5-a203-6ca150be6761"
}
2025-06-07 21:13:01 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 21:13:28 error: [GET] /api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sort=latest {
  "service": "campus-wall-api",
  "error": "\"[object Object]\" is not valid JSON",
  "stack": "SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at PostRepository.findById (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\repositories\\post.repository.js:36:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostService.getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:364:18)\n    at async getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:223:22)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "sort": "latest"
  }
}
2025-06-07 21:13:28 error: Request completed: GET /api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sort=latest {
  "service": "campus-wall-api",
  "requestId": "1749302008465-414go9td",
  "method": "GET",
  "url": "/api/posts/336b8bd5-9c11-480a-828d-d517742bdbda/comments?page=1&pageSize=10&sort=latest",
  "statusCode": 500,
  "duration": "4ms",
  "contentLength": "101"
}
2025-06-07 21:13:28 error: 增加浏览量失败: Cannot read properties of undefined (reading '0') {
  "service": "campus-wall-api",
  "postId": "336b8bd5-9c11-480a-828d-d517742bdbda"
}
2025-06-07 21:24:36 error: Uncaught Exception: listen EADDRINUSE: address already in use :::3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "::",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"
}
2025-06-07 21:32:06 error: Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "0.0.0.0",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"
}
2025-06-08 15:28:01 error: Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "0.0.0.0",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"
}
2025-06-08 15:28:04 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-08 15:28:14 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-08 15:28:24 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-08 15:28:35 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-08 15:28:45 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-08 15:28:55 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-08 15:29:05 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-08 16:15:32 error: [GET] / {
  "service": "campus-wall-api",
  "error": "未找到资源 - /",
  "stack": "Error: 未找到资源 - /\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-08 16:15:32 error: [GET] /favicon.ico {
  "service": "campus-wall-api",
  "error": "未找到资源 - /favicon.ico",
  "stack": "Error: 未找到资源 - /favicon.ico\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-08 17:25:07 error: [GET] /api/health {
  "service": "campus-wall-api",
  "error": "未找到资源 - /api/health",
  "stack": "Error: 未找到资源 - /api/health\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-08 18:57:12 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "12345678902",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-06-08 18:57:28 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "12345678902",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-06-08 19:39:56 error: [GET] /api/posts/post-1749382793918-1 {
  "service": "campus-wall-api",
  "error": "帖子不存在",
  "stack": "Error: 帖子不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at PostService.getPostById (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:119:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getPostDetail (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:58:20)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-08 20:18:54 error: [GET] /api/posts/undefined/comments?page=1&pageSize=10&sort=latest {
  "service": "campus-wall-api",
  "error": "帖子不存在",
  "stack": "Error: 帖子不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at PostService.getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:366:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:223:22)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "sort": "latest"
  }
}
2025-06-08 20:29:56 error: [GET] /[object%20Object] {
  "service": "campus-wall-api",
  "error": "未找到资源 - /[object%20Object]",
  "stack": "Error: 未找到资源 - /[object%20Object]\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-08 20:30:19 error: [GET] /[object%20Object] {
  "service": "campus-wall-api",
  "error": "未找到资源 - /[object%20Object]",
  "stack": "Error: 未找到资源 - /[object%20Object]\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-08 20:30:23 error: [GET] /[object%20Object] {
  "service": "campus-wall-api",
  "error": "未找到资源 - /[object%20Object]",
  "stack": "Error: 未找到资源 - /[object%20Object]\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:16:21\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-08 20:31:39 error: [GET] /api/posts/post-1749385892957-0 {
  "service": "campus-wall-api",
  "error": "帖子不存在",
  "stack": "Error: 帖子不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at PostService.getPostById (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:119:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getPostDetail (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:58:20)",
  "body": {},
  "params": {},
  "query": {}
}
2025-06-15 17:04:55 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:05:05 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:05:15 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:05:25 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:05:35 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:05:46 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:05:56 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:06:06 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:06:17 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:06:27 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:06:38 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:06:48 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:06:59 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:07:10 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:07:20 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-15 17:14:00 error: [GET] /api/posts/undefined/comments?page=1&pageSize=10&sort=latest {
  "service": "campus-wall-api",
  "error": "帖子不存在",
  "stack": "Error: 帖子不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at PostService.getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\post.service.js:366:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getPostComments (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\post.controller.js:223:22)",
  "body": {},
  "params": {},
  "query": {
    "page": "1",
    "pageSize": "10",
    "sort": "latest"
  }
}
2025-06-24 22:03:16 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-24 22:03:26 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-24 22:03:36 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-24 22:03:47 error: Redis connection error: connect ETIMEDOUT {
  "service": "campus-wall-api",
  "errorno": "ETIMEDOUT",
  "code": "ETIMEDOUT",
  "syscall": "connect",
  "stack": "Error: connect ETIMEDOUT\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\ioredis\\built\\Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:609:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"
}
2025-06-24 22:21:27 error: Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "0.0.0.0",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"
}
2025-06-24 22:29:23 error: 获取用户统计数据失败: Unknown column 'post.target_type' in 'on clause' {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_BAD_FIELD_ERROR",
    "errno": 1054,
    "sqlState": "42S22",
    "sqlMessage": "Unknown column 'post.target_type' in 'on clause'",
    "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');"
  },
  "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Like.aggregate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Like.count (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 1)\n    at async UserService.getUserStats (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:189:11)\n    at async UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:164:19)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)"
}
2025-06-24 22:29:23 error: 获取用户统计数据失败: Unknown column 'post.target_type' in 'on clause' {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_BAD_FIELD_ERROR",
    "errno": 1054,
    "sqlState": "42S22",
    "sqlMessage": "Unknown column 'post.target_type' in 'on clause'",
    "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');"
  },
  "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Like.aggregate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Like.count (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 1)\n    at async UserService.getUserStats (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:189:11)\n    at async UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:164:19)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)"
}
2025-06-24 22:29:25 error: 获取用户统计数据失败: Unknown column 'post.target_type' in 'on clause' {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_BAD_FIELD_ERROR",
    "errno": 1054,
    "sqlState": "42S22",
    "sqlMessage": "Unknown column 'post.target_type' in 'on clause'",
    "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');"
  },
  "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Like.aggregate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Like.count (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 1)\n    at async UserService.getUserStats (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:189:11)\n    at async UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:164:19)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)"
}
2025-06-24 22:29:34 error: 获取用户统计数据失败: Unknown column 'post.target_type' in 'on clause' {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_BAD_FIELD_ERROR",
    "errno": 1054,
    "sqlState": "42S22",
    "sqlMessage": "Unknown column 'post.target_type' in 'on clause'",
    "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');"
  },
  "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Like.aggregate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Like.count (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 1)\n    at async UserService.getUserStats (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:189:11)\n    at async UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:164:19)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)"
}
2025-06-24 22:29:36 error: 获取用户统计数据失败: Unknown column 'post.target_type' in 'on clause' {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_BAD_FIELD_ERROR",
    "errno": 1054,
    "sqlState": "42S22",
    "sqlMessage": "Unknown column 'post.target_type' in 'on clause'",
    "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');"
  },
  "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Like.aggregate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Like.count (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 1)\n    at async UserService.getUserStats (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:189:11)\n    at async UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:164:19)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)"
}
2025-06-24 22:29:37 error: 获取用户统计数据失败: Unknown column 'post.target_type' in 'on clause' {
  "service": "campus-wall-api",
  "name": "SequelizeDatabaseError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_BAD_FIELD_ERROR",
    "errno": 1054,
    "sqlState": "42S22",
    "sqlMessage": "Unknown column 'post.target_type' in 'on clause'",
    "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');"
  },
  "sql": "SELECT count(`Like`.`id`) AS `count` FROM `likes` AS `Like` INNER JOIN `posts` AS `post` ON `Like`.`target_id` = `post`.`id` AND (`post`.`deleted_at` IS NULL AND (`post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `post`.`target_type` = 'post')) WHERE (`Like`.`deleted_at` IS NULL AND `Like`.`target_type` = 'post');",
  "parameters": {},
  "stack": "Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Like.aggregate (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Like.count (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 1)\n    at async UserService.getUserStats (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:189:11)\n    at async UserService.getUserInfo (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:164:19)\n    at async getCurrentUser (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:61:20)"
}
2025-06-24 22:46:18 error: Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "0.0.0.0",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"
}
2025-06-24 23:10:11 error: Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3000 {
  "service": "campus-wall-api",
  "code": "EADDRINUSE",
  "errno": -4091,
  "syscall": "listen",
  "address": "0.0.0.0",
  "port": 3000,
  "stack": "Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"
}
2025-06-25 18:05:32 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "admin",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-06-25 18:05:40 error: [POST] /api/auth/login {
  "service": "campus-wall-api",
  "error": "用户不存在",
  "stack": "Error: 用户不存在\n    at ErrorMiddleware.createError (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\middlewares\\error.middleware.js:109:19)\n    at UserService.login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\services\\user.service.js:102:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\src\\controllers\\user.controller.js:44:22)",
  "body": {
    "username": "admin",
    "password": "123456"
  },
  "params": {},
  "query": {}
}
2025-06-26 13:00:50 error: Unable to connect to the database: Unknown database 'campus_community_test' {
  "service": "campus-wall-api",
  "name": "SequelizeConnectionError",
  "parent": "[Circular]",
  "original": {
    "code": "ER_BAD_DB_ERROR",
    "errno": 1049,
    "sqlState": "42000",
    "sqlMessage": "Unknown database 'campus_community_test'"
  },
  "stack": "SequelizeConnectionError: Unknown database 'campus_community_test'\n    at ConnectionManager.connect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\src\\dialects\\mysql\\connection-manager.js:126:17)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at ConnectionManager._connect (C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:332:24)\n    at C:\\Users\\<USER>\\Desktop\\校园墙\\server\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:250:32"
}
