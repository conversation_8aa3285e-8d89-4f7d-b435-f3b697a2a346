parser: "@typescript-eslint/parser"
parserOptions:
    ecmaVersion: 6
    sourceType: module
    ecmaFeatures:
        modules: true
    project: "tsconfig.json"
plugins: ["@typescript-eslint"]
env:
    browser: true
    node: true
    es6: false
globals:
    jQuery: true
    Promise: true
rules:
    no-console:
        - 2
        -
            allow:
                - "warn"
                - "error"
    no-constant-condition: 0
    comma-dangle: 2
    no-debugger: 2
    no-dupe-keys: 2
    no-empty-character-class: 2
    no-ex-assign: 2
    no-extra-boolean-cast: 0
    no-func-assign: 2
    no-inner-declarations: 2
    no-invalid-regexp: 2
    no-negated-in-lhs: 2
    no-obj-calls: 2
    no-sparse-arrays: 2
    no-unreachable: 2
    use-isnan: 2
    valid-typeof: 2
    block-scoped-var: 0
    curly:
        - 2
        - "all"
    eqeqeq:
        - 2
        - "allow-null"
    guard-for-in: 2
    no-else-return: 0
    no-labels:
        - 2
        -
            allowLoop: true
    no-eval: 2
    no-extend-native: 2
    no-extra-bind: 0
    no-implied-eval: 2
    no-iterator: 2
    no-irregular-whitespace: 2
    no-lone-blocks: 2
    no-loop-func: 2
    no-multi-str: 2
    no-native-reassign: 2
    no-new-wrappers: 2
    no-octal: 2
    no-octal-escape: 2
    no-proto: 2
    no-redeclare: 0
    no-self-compare: 2
    no-unneeded-ternary: 2
    no-with: 2
    radix: 2
    wrap-iife:
        - 2
        - "any"
    no-delete-var: 2
    no-dupe-args: 2
    no-duplicate-case: 2
    no-label-var: 2
    no-shadow-restricted-names: 2
    no-undef: 2
    no-undef-init: 2
    no-use-before-define: 0
    brace-style:
        - 2
        - "stroustrup"
        - {}
    comma-spacing:
        - 2
        -
            before: false
            after: true
    comma-style:
        - 2
        - "last"
    new-parens: 2
    no-array-constructor: 2
    no-multi-spaces:
        - 1
        -
            ignoreEOLComments: true
            exceptions:
                Property: true
    no-new-object: 2
    no-spaced-func: 2
    no-trailing-spaces: 2
    no-extra-parens:
        - 2
        - "functions"
    no-mixed-spaces-and-tabs: 2
    one-var:
        - 2
        - "never"
    operator-linebreak:
        - 2
        - "before"
        -
            overrides:
                "=": "after"
    quotes:
        - 2
        - "single"
    semi:
        - 2
        - "always"
    semi-spacing: 2
    keyword-spacing: 2
    key-spacing:
        - 2
        -
            beforeColon: false
            afterColon: true
    space-before-function-paren:
        - 2
        -
            anonymous: "always"
            named: "never"
    space-before-blocks:
        - 2
        - "always"
    computed-property-spacing:
        - 2
        - "never"
    space-in-parens:
        - 2
        - "never"
    space-unary-ops: 2
    spaced-comment: 0

    max-nested-callbacks:
        - 1
        - 5
    max-depth:
        - 1
        - 6
    max-len:
        - 2
        - 120
        - 4
        -
            ignoreUrls: true
            ignoreComments: true
    max-params:
        - 1
        - 15

    space-infix-ops: 2
    dot-notation:
        - 2
        -
            allowKeywords: true
            allowPattern: "^catch$"

    arrow-spacing: 2
    constructor-super: 2
    no-confusing-arrow:
        - 2
        -
            allowParens: true
    no-class-assign: 2
    no-const-assign: 2
    # no-dupe-class-members: 2
    no-this-before-super: 0
    no-var: 0
    no-duplicate-imports: 2
    prefer-rest-params: 0
    unicode-bom: 2
    max-statements-per-line: 2

    no-useless-constructor: 0


    "@typescript-eslint/no-unused-vars":
        - 1
        -
            vars: "local"
            args: "none"