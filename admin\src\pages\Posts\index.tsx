import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Image,
  Popconfirm,
  Tooltip,
  Typography,
  message,
} from 'antd'
import {
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PushpinOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  FileTextOutlined,
} from '@ant-design/icons'
import { formatTime, formatNumber, getPostStatusLabel } from '@/utils'
import type { Post } from '@/types'
import type { ColumnsType } from 'antd/es/table'
import './index.css'

const { Option } = Select
const { Text, Paragraph } = Typography

const Posts: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [posts, setPosts] = useState<Post[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    status: '',
    categoryId: '',
  })
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedPost, setSelectedPost] = useState<Post | null>(null)

  // 模拟帖子数据
  const mockPosts: Post[] = [
    {
      id: '1',
      title: '校园生活分享',
      content: '今天在校园里看到了美丽的樱花，春天真的来了！分享一些照片给大家。',
      images: [
        'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400',
        'https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=400',
      ],
      authorId: '2',
      categoryId: 1,
      status: 'published',
      viewCount: 156,
      likeCount: 23,
      commentCount: 8,
      favoriteCount: 12,
      isTop: false,
      createdAt: '2024-01-15T10:30:00.000Z',
      updatedAt: '2024-01-15T10:30:00.000Z',
      author: {
        id: '2',
        username: 'student01',
        avatar: '',
        role: 'student',
        isDisabled: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
      category: {
        id: 1,
        name: '校园生活',
        sort: 1,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
    },
    {
      id: '2',
      title: '学习经验分享：如何高效学习编程',
      content: '作为一名计算机专业的学生，我想分享一些学习编程的心得体会。首先，要多动手实践...',
      images: [],
      authorId: '3',
      categoryId: 2,
      status: 'published',
      viewCount: 89,
      likeCount: 15,
      commentCount: 5,
      favoriteCount: 8,
      isTop: true,
      createdAt: '2024-01-14T09:15:00.000Z',
      updatedAt: '2024-01-14T09:15:00.000Z',
      author: {
        id: '3',
        username: 'student02',
        avatar: '',
        role: 'student',
        isDisabled: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
      category: {
        id: 2,
        name: '学习交流',
        sort: 2,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
    },
    {
      id: '3',
      title: '社团活动通知：编程大赛报名开始',
      content: '各位同学，我们学院的编程大赛即将开始报名，欢迎大家踊跃参与！',
      images: [],
      authorId: '1',
      categoryId: 3,
      status: 'published',
      viewCount: 234,
      likeCount: 45,
      commentCount: 12,
      favoriteCount: 28,
      isTop: true,
      createdAt: '2024-01-13T08:45:00.000Z',
      updatedAt: '2024-01-13T08:45:00.000Z',
      author: {
        id: '1',
        username: 'admin',
        avatar: '',
        role: 'admin',
        isDisabled: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
      category: {
        id: 3,
        name: '活动通知',
        sort: 3,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
    },
  ]

  // 表格列定义
  const columns: ColumnsType<Post> = [
    {
      title: '帖子信息',
      key: 'postInfo',
      width: 300,
      render: (_, record) => (
        <div className="post-info-cell">
          <div className="post-header">
            <Text strong className="post-title" ellipsis={{ tooltip: record.title }}>
              {record.isTop && <PushpinOutlined className="pin-icon" />}
              {record.title || '无标题'}
            </Text>
            <Tag color="blue">{record.category?.name}</Tag>
          </div>
          <Paragraph
            className="post-content"
            ellipsis={{ rows: 2, tooltip: record.content }}
          >
            {record.content}
          </Paragraph>
          {record.images && record.images.length > 0 && (
            <div className="post-images">
              <Image.PreviewGroup>
                {record.images.slice(0, 3).map((img, index) => (
                  <Image
                    key={index}
                    width={40}
                    height={40}
                    src={img}
                    className="post-image-thumb"
                  />
                ))}
              </Image.PreviewGroup>
              {record.images.length > 3 && (
                <span className="more-images">+{record.images.length - 3}</span>
              )}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '作者',
      key: 'author',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.author?.username}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.author?.role === 'admin' ? '管理员' :
             record.author?.role === 'teacher' ? '教师' : '学生'}
          </Text>
        </div>
      ),
    },
    {
      title: '数据统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div className="post-stats">
          <div>浏览: {formatNumber(record.viewCount)}</div>
          <div>点赞: {formatNumber(record.likeCount)}</div>
          <div>评论: {formatNumber(record.commentCount)}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colorMap: Record<string, string> = {
          published: 'green',
          draft: 'orange',
          deleted: 'red',
        }
        return <Tag color={colorMap[status]}>{getPostStatusLabel(status)}</Tag>
      },
    },
    {
      title: '发布时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (time: string) => formatTime(time, 'MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title={record.isTop ? '取消置顶' : '置顶'}>
            <Popconfirm
              title={`确定要${record.isTop ? '取消置顶' : '置顶'}该帖子吗？`}
              onConfirm={() => handleToggleTop(record)}
            >
              <Button
                type="text"
                icon={<PushpinOutlined />}
                className={record.isTop ? 'pinned' : ''}
              />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="审核通过">
            <Button
              type="text"
              icon={<CheckCircleOutlined />}
              onClick={() => handleApprove(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除该帖子吗？此操作不可恢复！"
              onConfirm={() => handleDelete(record)}
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  // 加载帖子数据
  const loadPosts = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      setTimeout(() => {
        setPosts(mockPosts)
        setPagination(prev => ({ ...prev, total: mockPosts.length }))
        setLoading(false)
      }, 500)
    } catch (error) {
      message.error('加载帖子数据失败')
      setLoading(false)
    }
  }

  // 搜索帖子
  const handleSearch = () => {
    loadPosts()
  }

  // 重置搜索
  const handleReset = () => {
    setSearchParams({
      keyword: '',
      status: '',
      categoryId: '',
    })
    loadPosts()
  }

  // 查看详情
  const handleViewDetail = (post: Post) => {
    setSelectedPost(post)
    setDetailModalVisible(true)
  }

  // 切换置顶状态
  const handleToggleTop = (post: Post) => {
    message.success(`帖子${post.isTop ? '取消置顶' : '置顶'}成功`)
    loadPosts()
  }

  // 审核通过
  const handleApprove = (post: Post) => {
    message.success('帖子审核通过')
    loadPosts()
  }

  // 删除帖子
  const handleDelete = (post: Post) => {
    message.success('帖子删除成功')
    loadPosts()
  }

  // 表格分页变化
  const handleTableChange = (pagination: any) => {
    setPagination(pagination)
    loadPosts()
  }

  useEffect(() => {
    loadPosts()
  }, [])

  return (
    <div className="posts-page">
      {/* 搜索区域 */}
      <Card className="search-card mb-16">
        <div className="search-form">
          <Space size="middle" wrap>
            <Input
              placeholder="搜索帖子标题、内容"
              prefix={<SearchOutlined />}
              value={searchParams.keyword}
              onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
              style={{ width: 250 }}
            />
            <Select
              placeholder="选择状态"
              value={searchParams.status}
              onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
              style={{ width: 120 }}
              allowClear
            >
              <Option value="published">已发布</Option>
              <Option value="draft">草稿</Option>
              <Option value="deleted">已删除</Option>
            </Select>
            <Select
              placeholder="选择分类"
              value={searchParams.categoryId}
              onChange={(value) => setSearchParams(prev => ({ ...prev, categoryId: value }))}
              style={{ width: 120 }}
              allowClear
            >
              <Option value="1">校园生活</Option>
              <Option value="2">学习交流</Option>
              <Option value="3">活动通知</Option>
            </Select>
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>
      </Card>

      {/* 帖子列表 */}
      <Card title="帖子列表">
        <Table
          columns={columns}
          dataSource={posts}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 帖子详情模态框 */}
      <Modal
        title="帖子详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedPost && (
          <div className="post-detail">
            <div className="post-detail-header">
              <h3>{selectedPost.title || '无标题'}</h3>
              <div className="post-meta">
                <Space>
                  <Tag color="blue">{selectedPost.category?.name}</Tag>
                  <Text type="secondary">
                    作者: {selectedPost.author?.username}
                  </Text>
                  <Text type="secondary">
                    发布时间: {formatTime(selectedPost.createdAt)}
                  </Text>
                </Space>
              </div>
            </div>
            <div className="post-detail-content">
              <p>{selectedPost.content}</p>
              {selectedPost.images && selectedPost.images.length > 0 && (
                <div className="post-detail-images">
                  <Image.PreviewGroup>
                    {selectedPost.images.map((img, index) => (
                      <Image
                        key={index}
                        width={200}
                        src={img}
                        style={{ marginRight: 8, marginBottom: 8 }}
                      />
                    ))}
                  </Image.PreviewGroup>
                </div>
              )}
            </div>
            <div className="post-detail-stats">
              <Space size="large">
                <span>浏览量: {formatNumber(selectedPost.viewCount)}</span>
                <span>点赞数: {formatNumber(selectedPost.likeCount)}</span>
                <span>评论数: {formatNumber(selectedPost.commentCount)}</span>
                <span>收藏数: {formatNumber(selectedPost.favoriteCount)}</span>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Posts
