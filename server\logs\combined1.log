2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:45 info: Request completed: GET /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "requestId": "1750775565444-84kd88sa",
  "method": "GET",
  "url": "/api/posts/user/me?page=1&pageSize=10&type=published",
  "statusCode": 304,
  "duration": "63ms",
  "contentLength": 0
}
2025-06-24 22:32:46 info: Request started: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775566267-u7a2fjl8",
  "method": "GET",
  "url": "/api/users/me",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:46 debug: Executing (default): SELECT `id`, `username`, `nickname`, `phone`, `email`, `avatar`, `role`, `gender`, `bio`, `school`, `department`, `is_disabled`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt`, `deleted_at` AS `deletedAt` FROM `users` AS `User` WHERE (`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": false,
    "showWarnings": false,
    "where": "(`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "rejectOnEmpty": false,
    "attributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      [
        "created_at",
        "createdAt"
      ],
      [
        "updated_at",
        "updatedAt"
      ],
      [
        "deleted_at",
        "deletedAt"
      ]
    ],
    "originalAttributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      "createdAt",
      "updatedAt",
      "deletedAt"
    ],
    "tableNames": [
      "users"
    ],
    "type": "SELECT",
    "model": "[Circular]"
  }
}
2025-06-24 22:32:46 info: Request started: GET /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "requestId": "1750775566272-edvypvf3",
  "method": "GET",
  "url": "/api/posts/user/me?page=1&pageSize=10&type=published",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(DISTINCT(`Post`.`id`)) AS `count` FROM `posts` AS `Post` INNER JOIN `users` AS `author` ON `Post`.`user_id` = `author`.`id` AND `author`.`deleted_at` IS NULL LEFT OUTER JOIN `categories` AS `category` ON `Post`.`category_id` = `category`.`id` AND (`category`.`deleted_at` IS NULL) LEFT OUTER JOIN ( `post_topics` AS `topics->post_topics` INNER JOIN `topics` AS `topics` ON `topics`.`id` = `topics->post_topics`.`topic_id`) ON `Post`.`id` = `topics->post_topics`.`post_id` AND (`topics`.`deleted_at` IS NULL) WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "limit": null,
    "offset": null,
    "order": null,
    "distinct": true,
    "include": "[Circular]",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "fn": "DISTINCT",
              "args": [
                {
                  "col": "Post.id"
                }
              ]
            }
          ]
        },
        "count"
      ]
    ],
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            "ASC"
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": null,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": null,
    "hasDuplicating": true,
    "hasRequired": true,
    "hasWhere": true,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "subQuery": false,
    "type": "SELECT",
    "keysEscaped": true
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT `Post`.*, `category`.`id` AS `category.id`, `category`.`name` AS `category.name`, `category`.`icon` AS `category.icon`, `topics`.`id` AS `topics.id`, `topics`.`name` AS `topics.name` FROM (SELECT `Post`.`id`, `Post`.`title`, `Post`.`content`, `Post`.`user_id`, `Post`.`category_id`, `Post`.`status`, `Post`.`view_count`, `Post`.`like_count`, `Post`.`comment_count`, `Post`.`favorite_count`, `Post`.`is_top`, `Post`.`location_name`, `Post`.`longitude`, `Post`.`latitude`, `Post`.`created_at` AS `createdAt`, `Post`.`updated_at` AS `updatedAt`, `Post`.`deleted_at` AS `deletedAt`, `author`.`id` AS `author.id`, `author`.`username` AS `author.username`, `author`.`nickname` AS `author.nickname`, `author`.`avatar` AS `author.avatar`, `author`.`school` AS `author.school`, `author`.`department` AS `author.department` FROM `posts` AS `Post` INNER JOIN `users` AS `author` ON `Post`.`user_id` = `author`.`id` AND `author`.`deleted_at` IS NULL WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')) ORDER BY `Post`.`is_top` DESC, `Post`.`created_at` DESC LIMIT 0, 10) AS `Post` LEFT OUTER JOIN `categories` AS `category` ON `Post`.`category_id` = `category`.`id` AND (`category`.`deleted_at` IS NULL) LEFT OUTER JOIN ( `post_topics` AS `topics->post_topics` INNER JOIN `topics` AS `topics` ON `topics`.`id` = `topics->post_topics`.`topic_id`) ON `Post`.`id` = `topics->post_topics`.`post_id` AND (`topics`.`deleted_at` IS NULL) ORDER BY `Post`.`is_top` DESC, `createdAt` DESC; {
  "service": "campus-wall-api",
  "timing": {
    "plain": false,
    "raw": false,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "limit": 10,
    "offset": 0,
    "order": [
      [
        "is_top",
        {
          "val": " DESC"
        }
      ],
      [
        {
          "col": "createdAt"
        },
        {
          "val": " DESC"
        }
      ]
    ],
    "distinct": true,
    "include": "[Circular]",
    "hooks": true,
    "rejectOnEmpty": false,
    "originalAttributes": [
      "id",
      "title",
      "content",
      "user_id",
      "category_id",
      "status",
      "view_count",
      "like_count",
      "comment_count",
      "favorite_count",
      "is_top",
      "location_name",
      "longitude",
      "latitude",
      "createdAt",
      "updatedAt",
      "deletedAt"
    ],
    "hasJoin": true,
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": true,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name",
          "icon"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            "ASC"
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": 10,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": 10,
    "hasDuplicating": true,
    "hasRequired": true,
    "hasWhere": true,
    "subQuery": true,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "attributes": [
      "id",
      "title",
      "content",
      "user_id",
      "category_id",
      "status",
      "view_count",
      "like_count",
      "comment_count",
      "favorite_count",
      "is_top",
      "location_name",
      "longitude",
      "latitude",
      [
        "created_at",
        "createdAt"
      ],
      [
        "updated_at",
        "updatedAt"
      ],
      [
        "deleted_at",
        "deletedAt"
      ]
    ],
    "tableNames": [
      "posts",
      "users",
      "categories",
      "post_images",
      "topics",
      "undefined",
      "post_topics"
    ],
    "type": "SELECT",
    "keysEscaped": true
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `posts` AS `Post` WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published'))); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published')))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT `PostImage`.* FROM (SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub) AS `PostImage`; {
  "service": "campus-wall-api",
  "timing": {
    "plain": false,
    "raw": false,
    "showWarnings": false,
    "distinct": true,
    "hooks": true,
    "rejectOnEmpty": false,
    "hasJoin": true,
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": true,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name",
          "icon"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            {
              "val": " ASC"
            }
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": 10,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": 10,
    "hasDuplicating": false,
    "hasRequired": false,
    "hasWhere": true,
    "subQuery": false,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "attributes": [
      "id",
      "url",
      "thumbnail_url",
      "width",
      "height",
      "order",
      "post_id"
    ],
    "separate": true,
    "required": false,
    "duplicating": false,
    "hasParentWhere": true,
    "hasParentRequired": false,
    "subQueryFilter": false,
    "where": {},
    "groupedLimit": {
      "limit": 9,
      "on": "[Circular]",
      "values": [
        "4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6",
        "336b8bd5-9c11-480a-828d-d517742bdbda",
        "7a76d7db-3ac9-4a25-b86f-10b8eebde4b1",
        "8507e97c-a576-4ec5-a203-6ca150be6761",
        "69357df6-5b0d-4b17-a076-0a371b9a6c45",
        "b53e93e7-b200-45e6-977b-2f37ff46612c",
        "9a054364-45a5-4d19-88f5-bb8ef80f73a4",
        "08f6b257-c66d-4390-8aa4-ee9cbaf27c01",
        "5c561b0d-56c9-4ca4-ba33-c02fe000c149"
      ]
    },
    "originalAttributes": "[Circular]",
    "tableNames": [
      "post_images"
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 info: Request completed: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775566267-u7a2fjl8",
  "method": "GET",
  "url": "/api/users/me",
  "statusCode": 304,
  "duration": "49ms",
  "contentLength": 0
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:46 info: Request completed: GET /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "requestId": "1750775566272-edvypvf3",
  "method": "GET",
  "url": "/api/posts/user/me?page=1&pageSize=10&type=published",
  "statusCode": 304,
  "duration": "76ms",
  "contentLength": 0
}
2025-06-24 22:32:47 info: Request started: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775567362-uh6inu46",
  "method": "GET",
  "url": "/api/users/me",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:47 debug: Executing (default): SELECT `id`, `username`, `nickname`, `phone`, `email`, `avatar`, `role`, `gender`, `bio`, `school`, `department`, `is_disabled`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt`, `deleted_at` AS `deletedAt` FROM `users` AS `User` WHERE (`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": false,
    "showWarnings": false,
    "where": "(`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "rejectOnEmpty": false,
    "attributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      [
        "created_at",
        "createdAt"
      ],
      [
        "updated_at",
        "updatedAt"
      ],
      [
        "deleted_at",
        "deletedAt"
      ]
    ],
    "originalAttributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      "createdAt",
      "updatedAt",
      "deletedAt"
    ],
    "tableNames": [
      "users"
    ],
    "type": "SELECT",
    "model": "[Circular]"
  }
}
2025-06-24 22:32:47 info: Request started: GET /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "requestId": "1750775567368-8vtesbxo",
  "method": "GET",
  "url": "/api/posts/user/me?page=1&pageSize=10&type=published",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(DISTINCT(`Post`.`id`)) AS `count` FROM `posts` AS `Post` INNER JOIN `users` AS `author` ON `Post`.`user_id` = `author`.`id` AND `author`.`deleted_at` IS NULL LEFT OUTER JOIN `categories` AS `category` ON `Post`.`category_id` = `category`.`id` AND (`category`.`deleted_at` IS NULL) LEFT OUTER JOIN ( `post_topics` AS `topics->post_topics` INNER JOIN `topics` AS `topics` ON `topics`.`id` = `topics->post_topics`.`topic_id`) ON `Post`.`id` = `topics->post_topics`.`post_id` AND (`topics`.`deleted_at` IS NULL) WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "limit": null,
    "offset": null,
    "order": null,
    "distinct": true,
    "include": "[Circular]",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "fn": "DISTINCT",
              "args": [
                {
                  "col": "Post.id"
                }
              ]
            }
          ]
        },
        "count"
      ]
    ],
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            "ASC"
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": null,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": null,
    "hasDuplicating": true,
    "hasRequired": true,
    "hasWhere": true,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "subQuery": false,
    "type": "SELECT",
    "keysEscaped": true
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT `Post`.*, `category`.`id` AS `category.id`, `category`.`name` AS `category.name`, `category`.`icon` AS `category.icon`, `topics`.`id` AS `topics.id`, `topics`.`name` AS `topics.name` FROM (SELECT `Post`.`id`, `Post`.`title`, `Post`.`content`, `Post`.`user_id`, `Post`.`category_id`, `Post`.`status`, `Post`.`view_count`, `Post`.`like_count`, `Post`.`comment_count`, `Post`.`favorite_count`, `Post`.`is_top`, `Post`.`location_name`, `Post`.`longitude`, `Post`.`latitude`, `Post`.`created_at` AS `createdAt`, `Post`.`updated_at` AS `updatedAt`, `Post`.`deleted_at` AS `deletedAt`, `author`.`id` AS `author.id`, `author`.`username` AS `author.username`, `author`.`nickname` AS `author.nickname`, `author`.`avatar` AS `author.avatar`, `author`.`school` AS `author.school`, `author`.`department` AS `author.department` FROM `posts` AS `Post` INNER JOIN `users` AS `author` ON `Post`.`user_id` = `author`.`id` AND `author`.`deleted_at` IS NULL WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')) ORDER BY `Post`.`is_top` DESC, `Post`.`created_at` DESC LIMIT 0, 10) AS `Post` LEFT OUTER JOIN `categories` AS `category` ON `Post`.`category_id` = `category`.`id` AND (`category`.`deleted_at` IS NULL) LEFT OUTER JOIN ( `post_topics` AS `topics->post_topics` INNER JOIN `topics` AS `topics` ON `topics`.`id` = `topics->post_topics`.`topic_id`) ON `Post`.`id` = `topics->post_topics`.`post_id` AND (`topics`.`deleted_at` IS NULL) ORDER BY `Post`.`is_top` DESC, `createdAt` DESC; {
  "service": "campus-wall-api",
  "timing": {
    "plain": false,
    "raw": false,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "limit": 10,
    "offset": 0,
    "order": [
      [
        "is_top",
        {
          "val": " DESC"
        }
      ],
      [
        {
          "col": "createdAt"
        },
        {
          "val": " DESC"
        }
      ]
    ],
    "distinct": true,
    "include": "[Circular]",
    "hooks": true,
    "rejectOnEmpty": false,
    "originalAttributes": [
      "id",
      "title",
      "content",
      "user_id",
      "category_id",
      "status",
      "view_count",
      "like_count",
      "comment_count",
      "favorite_count",
      "is_top",
      "location_name",
      "longitude",
      "latitude",
      "createdAt",
      "updatedAt",
      "deletedAt"
    ],
    "hasJoin": true,
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": true,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name",
          "icon"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            "ASC"
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": 10,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": 10,
    "hasDuplicating": true,
    "hasRequired": true,
    "hasWhere": true,
    "subQuery": true,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "attributes": [
      "id",
      "title",
      "content",
      "user_id",
      "category_id",
      "status",
      "view_count",
      "like_count",
      "comment_count",
      "favorite_count",
      "is_top",
      "location_name",
      "longitude",
      "latitude",
      [
        "created_at",
        "createdAt"
      ],
      [
        "updated_at",
        "updatedAt"
      ],
      [
        "deleted_at",
        "deletedAt"
      ]
    ],
    "tableNames": [
      "posts",
      "users",
      "categories",
      "post_images",
      "topics",
      "undefined",
      "post_topics"
    ],
    "type": "SELECT",
    "keysEscaped": true
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `posts` AS `Post` WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published'))); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published')))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT `PostImage`.* FROM (SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub) AS `PostImage`; {
  "service": "campus-wall-api",
  "timing": {
    "plain": false,
    "raw": false,
    "showWarnings": false,
    "distinct": true,
    "hooks": true,
    "rejectOnEmpty": false,
    "hasJoin": true,
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": true,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name",
          "icon"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            {
              "val": " ASC"
            }
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": 10,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": 10,
    "hasDuplicating": false,
    "hasRequired": false,
    "hasWhere": true,
    "subQuery": false,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "attributes": [
      "id",
      "url",
      "thumbnail_url",
      "width",
      "height",
      "order",
      "post_id"
    ],
    "separate": true,
    "required": false,
    "duplicating": false,
    "hasParentWhere": true,
    "hasParentRequired": false,
    "subQueryFilter": false,
    "where": {},
    "groupedLimit": {
      "limit": 9,
      "on": "[Circular]",
      "values": [
        "4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6",
        "336b8bd5-9c11-480a-828d-d517742bdbda",
        "7a76d7db-3ac9-4a25-b86f-10b8eebde4b1",
        "8507e97c-a576-4ec5-a203-6ca150be6761",
        "69357df6-5b0d-4b17-a076-0a371b9a6c45",
        "b53e93e7-b200-45e6-977b-2f37ff46612c",
        "9a054364-45a5-4d19-88f5-bb8ef80f73a4",
        "08f6b257-c66d-4390-8aa4-ee9cbaf27c01",
        "5c561b0d-56c9-4ca4-ba33-c02fe000c149"
      ]
    },
    "originalAttributes": "[Circular]",
    "tableNames": [
      "post_images"
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 info: Request completed: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775567362-uh6inu46",
  "method": "GET",
  "url": "/api/users/me",
  "statusCode": 304,
  "duration": "60ms",
  "contentLength": 0
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:47 info: Request completed: GET /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "requestId": "1750775567368-8vtesbxo",
  "method": "GET",
  "url": "/api/posts/user/me?page=1&pageSize=10&type=published",
  "statusCode": 304,
  "duration": "81ms",
  "contentLength": 0
}
2025-06-24 22:32:49 info: Request started: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775569355-1kwnd83g",
  "method": "GET",
  "url": "/api/users/me",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:49 debug: Executing (default): SELECT `id`, `username`, `nickname`, `phone`, `email`, `avatar`, `role`, `gender`, `bio`, `school`, `department`, `is_disabled`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt`, `deleted_at` AS `deletedAt` FROM `users` AS `User` WHERE (`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": false,
    "showWarnings": false,
    "where": "(`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "rejectOnEmpty": false,
    "attributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      [
        "created_at",
        "createdAt"
      ],
      [
        "updated_at",
        "updatedAt"
      ],
      [
        "deleted_at",
        "deletedAt"
      ]
    ],
    "originalAttributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      "createdAt",
      "updatedAt",
      "deletedAt"
    ],
    "tableNames": [
      "users"
    ],
    "type": "SELECT",
    "model": "[Circular]"
  }
}
2025-06-24 22:32:49 info: Request started: GET /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "requestId": "1750775569359-z4n4ivrr",
  "method": "GET",
  "url": "/api/posts/user/me?page=1&pageSize=10&type=published",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(DISTINCT(`Post`.`id`)) AS `count` FROM `posts` AS `Post` INNER JOIN `users` AS `author` ON `Post`.`user_id` = `author`.`id` AND `author`.`deleted_at` IS NULL LEFT OUTER JOIN `categories` AS `category` ON `Post`.`category_id` = `category`.`id` AND (`category`.`deleted_at` IS NULL) LEFT OUTER JOIN ( `post_topics` AS `topics->post_topics` INNER JOIN `topics` AS `topics` ON `topics`.`id` = `topics->post_topics`.`topic_id`) ON `Post`.`id` = `topics->post_topics`.`post_id` AND (`topics`.`deleted_at` IS NULL) WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "limit": null,
    "offset": null,
    "order": null,
    "distinct": true,
    "include": "[Circular]",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "fn": "DISTINCT",
              "args": [
                {
                  "col": "Post.id"
                }
              ]
            }
          ]
        },
        "count"
      ]
    ],
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            "ASC"
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": null,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": null,
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": null,
    "hasDuplicating": true,
    "hasRequired": true,
    "hasWhere": true,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "subQuery": false,
    "type": "SELECT",
    "keysEscaped": true
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT `Post`.*, `category`.`id` AS `category.id`, `category`.`name` AS `category.name`, `category`.`icon` AS `category.icon`, `topics`.`id` AS `topics.id`, `topics`.`name` AS `topics.name` FROM (SELECT `Post`.`id`, `Post`.`title`, `Post`.`content`, `Post`.`user_id`, `Post`.`category_id`, `Post`.`status`, `Post`.`view_count`, `Post`.`like_count`, `Post`.`comment_count`, `Post`.`favorite_count`, `Post`.`is_top`, `Post`.`location_name`, `Post`.`longitude`, `Post`.`latitude`, `Post`.`created_at` AS `createdAt`, `Post`.`updated_at` AS `updatedAt`, `Post`.`deleted_at` AS `deletedAt`, `author`.`id` AS `author.id`, `author`.`username` AS `author.username`, `author`.`nickname` AS `author.nickname`, `author`.`avatar` AS `author.avatar`, `author`.`school` AS `author.school`, `author`.`department` AS `author.department` FROM `posts` AS `Post` INNER JOIN `users` AS `author` ON `Post`.`user_id` = `author`.`id` AND `author`.`deleted_at` IS NULL WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')) ORDER BY `Post`.`is_top` DESC, `Post`.`created_at` DESC LIMIT 0, 10) AS `Post` LEFT OUTER JOIN `categories` AS `category` ON `Post`.`category_id` = `category`.`id` AND (`category`.`deleted_at` IS NULL) LEFT OUTER JOIN ( `post_topics` AS `topics->post_topics` INNER JOIN `topics` AS `topics` ON `topics`.`id` = `topics->post_topics`.`topic_id`) ON `Post`.`id` = `topics->post_topics`.`post_id` AND (`topics`.`deleted_at` IS NULL) ORDER BY `Post`.`is_top` DESC, `createdAt` DESC; {
  "service": "campus-wall-api",
  "timing": {
    "plain": false,
    "raw": false,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`status` = 'published' AND `Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "limit": 10,
    "offset": 0,
    "order": [
      [
        "is_top",
        {
          "val": " DESC"
        }
      ],
      [
        {
          "col": "createdAt"
        },
        {
          "val": " DESC"
        }
      ]
    ],
    "distinct": true,
    "include": "[Circular]",
    "hooks": true,
    "rejectOnEmpty": false,
    "originalAttributes": [
      "id",
      "title",
      "content",
      "user_id",
      "category_id",
      "status",
      "view_count",
      "like_count",
      "comment_count",
      "favorite_count",
      "is_top",
      "location_name",
      "longitude",
      "latitude",
      "createdAt",
      "updatedAt",
      "deletedAt"
    ],
    "hasJoin": true,
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": true,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name",
          "icon"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            "ASC"
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": 10,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": 10,
    "hasDuplicating": true,
    "hasRequired": true,
    "hasWhere": true,
    "subQuery": true,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "attributes": [
      "id",
      "title",
      "content",
      "user_id",
      "category_id",
      "status",
      "view_count",
      "like_count",
      "comment_count",
      "favorite_count",
      "is_top",
      "location_name",
      "longitude",
      "latitude",
      [
        "created_at",
        "createdAt"
      ],
      [
        "updated_at",
        "updatedAt"
      ],
      [
        "deleted_at",
        "deletedAt"
      ]
    ],
    "tableNames": [
      "posts",
      "users",
      "categories",
      "post_images",
      "topics",
      "undefined",
      "post_topics"
    ],
    "type": "SELECT",
    "keysEscaped": true
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `posts` AS `Post` WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published'))); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published')))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT `PostImage`.* FROM (SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub UNION ALL SELECT * FROM (SELECT `id`, `url`, `thumbnail_url`, `width`, `height`, `order`, `post_id` FROM `post_images` AS `PostImage` WHERE (`PostImage`.`deleted_at` IS NULL AND ((`PostImage`.`deleted_at` IS NULL))) AND `PostImage`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' ORDER BY `PostImage`.`order` ASC LIMIT 9) AS sub) AS `PostImage`; {
  "service": "campus-wall-api",
  "timing": {
    "plain": false,
    "raw": false,
    "showWarnings": false,
    "distinct": true,
    "hooks": true,
    "rejectOnEmpty": false,
    "hasJoin": true,
    "model": "[Circular]",
    "includeNames": [
      "author",
      "category",
      "images",
      "topics"
    ],
    "includeMap": {
      "author": {
        "model": "[Circular]",
        "as": "author",
        "attributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "where": {
          "deleted_at": {}
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "username",
          "nickname",
          "avatar",
          "school",
          "department"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "author",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "user_id",
          "identifier": "user_id",
          "identifierField": "user_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "author",
          "accessors": {
            "get": "getAuthor",
            "set": "setAuthor",
            "create": "createAuthor"
          }
        },
        "required": true,
        "hasRequired": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": true,
        "subQueryFilter": false
      },
      "category": {
        "model": "[Circular]",
        "as": "category",
        "attributes": [
          "id",
          "name",
          "icon"
        ],
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name",
          "icon"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "category",
          "associationType": "BelongsTo",
          "isSingleAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "category_id",
          "identifier": "category_id",
          "identifierField": "category_id",
          "targetKey": "id",
          "targetKeyField": "id",
          "targetKeyIsPrimary": true,
          "targetIdentifier": "id",
          "associationAccessor": "category",
          "accessors": {
            "get": "getCategory",
            "set": "setCategory",
            "create": "createCategory"
          }
        },
        "required": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "images": {
        "model": "[Circular]",
        "as": "images",
        "attributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order",
          "post_id"
        ],
        "order": [
          [
            "order",
            {
              "val": " ASC"
            }
          ]
        ],
        "separate": true,
        "limit": 9,
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "url",
          "thumbnail_url",
          "width",
          "height",
          "order"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "images",
          "associationType": "HasMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "isMultiAssociation": true,
          "foreignKeyAttribute": {},
          "isAliased": true,
          "foreignKey": "post_id",
          "identifierField": "post_id",
          "foreignKeyField": "post_id",
          "sourceKey": "id",
          "sourceKeyAttribute": "id",
          "sourceKeyField": "id",
          "associationAccessor": "images",
          "accessors": {
            "get": "getImages",
            "set": "setImages",
            "addMultiple": "addImages",
            "add": "addImage",
            "create": "createImage",
            "remove": "removeImage",
            "removeMultiple": "removeImages",
            "hasSingle": "hasImage",
            "hasAll": "hasImages",
            "count": "countImages"
          }
        },
        "required": false,
        "duplicating": false,
        "hasDuplicating": false,
        "hasRequired": false,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      },
      "topics": {
        "model": "[Circular]",
        "as": "topics",
        "attributes": [
          "id",
          "name"
        ],
        "through": {
          "attributes": [],
          "model": "[Circular]",
          "as": "post_topics",
          "association": {
            "isSingleAssociation": true
          },
          "_pseudo": true,
          "parent": "[Circular]",
          "topLimit": 10,
          "originalAttributes": [],
          "hasParentWhere": false,
          "hasParentRequired": false,
          "subQuery": false,
          "subQueryFilter": false
        },
        "parent": "[Circular]",
        "topLimit": 10,
        "originalAttributes": [
          "id",
          "name"
        ],
        "association": {
          "options": "[Circular]",
          "isSelfAssociation": false,
          "as": "topics",
          "associationType": "BelongsToMany",
          "targetAssociation": null,
          "sequelize": "[Circular]",
          "through": {
            "model": "[Circular]"
          },
          "isMultiAssociation": true,
          "doubleLinked": false,
          "isAliased": true,
          "combinedTableName": "poststopics",
          "sourceKey": "id",
          "sourceKeyField": "id",
          "targetKeyDefault": true,
          "targetKey": "id",
          "targetKeyField": "id",
          "foreignKeyAttribute": {},
          "foreignKey": "post_id",
          "otherKeyAttribute": {},
          "otherKey": "topic_id",
          "combinedName": "post_topics",
          "associationAccessor": "topics",
          "accessors": {
            "get": "getTopics",
            "set": "setTopics",
            "addMultiple": "addTopics",
            "add": "addTopic",
            "create": "createTopic",
            "remove": "removeTopic",
            "removeMultiple": "removeTopics",
            "hasSingle": "hasTopic",
            "hasAll": "hasTopics",
            "count": "countTopics"
          },
          "identifier": "post_id",
          "foreignIdentifier": "topic_id",
          "primaryKeyDeleted": true,
          "identifierField": "post_id",
          "foreignIdentifierField": "topic_id",
          "toSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Post",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifier": "post_id",
            "identifierField": "post_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Post",
            "accessors": {
              "get": "getPost",
              "set": "setPost",
              "create": "createPost"
            }
          },
          "manyFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "post_id",
            "identifierField": "post_id",
            "foreignKeyField": "post_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromSource": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "post_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "post_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "toTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "Topic",
            "associationType": "BelongsTo",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifier": "topic_id",
            "identifierField": "topic_id",
            "targetKey": "id",
            "targetKeyField": "id",
            "targetKeyIsPrimary": true,
            "targetIdentifier": "id",
            "associationAccessor": "Topic",
            "accessors": {
              "get": "getTopic",
              "set": "setTopic",
              "create": "createTopic"
            }
          },
          "manyFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "topics",
            "associationType": "HasMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "isMultiAssociation": true,
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "identifierField": "topic_id",
            "foreignKeyField": "topic_id",
            "sourceKey": "id",
            "sourceKeyAttribute": "id",
            "sourceKeyField": "id",
            "associationAccessor": "topics",
            "accessors": {
              "get": "getTopics",
              "set": "setTopics",
              "addMultiple": "addTopics",
              "add": "addTopic",
              "create": "createTopic",
              "remove": "removeTopic",
              "removeMultiple": "removeTopics",
              "hasSingle": "hasTopic",
              "hasAll": "hasTopics",
              "count": "countTopics"
            }
          },
          "oneFromTarget": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "post_topics",
            "associationType": "HasOne",
            "isSingleAssociation": true,
            "foreignKeyAttribute": {},
            "isAliased": true,
            "foreignKey": "topic_id",
            "sourceKeyAttribute": "id",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "sourceKeyIsPrimary": true,
            "associationAccessor": "post_topics",
            "identifierField": "topic_id",
            "accessors": {
              "get": "getPost_topics",
              "set": "setPost_topics",
              "create": "createPost_topics"
            }
          },
          "paired": {
            "options": "[Circular]",
            "isSelfAssociation": false,
            "as": "posts",
            "associationType": "BelongsToMany",
            "targetAssociation": null,
            "sequelize": "[Circular]",
            "through": {
              "model": "[Circular]"
            },
            "isMultiAssociation": true,
            "doubleLinked": false,
            "isAliased": true,
            "combinedTableName": "poststopics",
            "paired": "[Circular]",
            "sourceKey": "id",
            "sourceKeyField": "id",
            "targetKeyDefault": true,
            "targetKey": "id",
            "targetKeyField": "id",
            "foreignKeyAttribute": {},
            "foreignKey": "topic_id",
            "otherKeyAttribute": {},
            "otherKey": "post_id",
            "combinedName": "post_topics",
            "associationAccessor": "posts",
            "accessors": {
              "get": "getPosts",
              "set": "setPosts",
              "addMultiple": "addPosts",
              "add": "addPost",
              "create": "createPost",
              "remove": "removePost",
              "removeMultiple": "removePosts",
              "hasSingle": "hasPost",
              "hasAll": "hasPosts",
              "count": "countPosts"
            },
            "identifier": "topic_id",
            "foreignIdentifier": "post_id",
            "primaryKeyDeleted": true,
            "identifierField": "topic_id",
            "foreignIdentifierField": "post_id",
            "toSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Topic",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifier": "topic_id",
              "identifierField": "topic_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Topic",
              "accessors": {
                "get": "getTopic",
                "set": "setTopic",
                "create": "createTopic"
              }
            },
            "manyFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "topic_id",
              "identifierField": "topic_id",
              "foreignKeyField": "topic_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromSource": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "topic_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "topic_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            },
            "toTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "Post",
              "associationType": "BelongsTo",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifier": "post_id",
              "identifierField": "post_id",
              "targetKey": "id",
              "targetKeyField": "id",
              "targetKeyIsPrimary": true,
              "targetIdentifier": "id",
              "associationAccessor": "Post",
              "accessors": {
                "get": "getPost",
                "set": "setPost",
                "create": "createPost"
              }
            },
            "manyFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "topics",
              "associationType": "HasMany",
              "targetAssociation": null,
              "sequelize": "[Circular]",
              "isMultiAssociation": true,
              "foreignKeyAttribute": {},
              "foreignKey": "post_id",
              "identifierField": "post_id",
              "foreignKeyField": "post_id",
              "sourceKey": "id",
              "sourceKeyAttribute": "id",
              "sourceKeyField": "id",
              "associationAccessor": "topics",
              "accessors": {
                "get": "getTopics",
                "set": "setTopics",
                "addMultiple": "addTopics",
                "add": "addTopic",
                "create": "createTopic",
                "remove": "removeTopic",
                "removeMultiple": "removeTopics",
                "hasSingle": "hasTopic",
                "hasAll": "hasTopics",
                "count": "countTopics"
              }
            },
            "oneFromTarget": {
              "options": "[Circular]",
              "isSelfAssociation": false,
              "as": "post_topics",
              "associationType": "HasOne",
              "isSingleAssociation": true,
              "foreignKeyAttribute": {},
              "isAliased": true,
              "foreignKey": "post_id",
              "sourceKeyAttribute": "id",
              "sourceKey": "id",
              "sourceKeyField": "id",
              "sourceKeyIsPrimary": true,
              "associationAccessor": "post_topics",
              "identifierField": "post_id",
              "accessors": {
                "get": "getPost_topics",
                "set": "setPost_topics",
                "create": "createPost_topics"
              }
            }
          }
        },
        "include": "[Circular]",
        "required": false,
        "includeNames": [
          "post_topics"
        ],
        "includeMap": {
          "post_topics": "[Circular]"
        },
        "hasSingleAssociation": true,
        "hasMultiAssociation": false,
        "hasDuplicating": true,
        "hasRequired": false,
        "hasWhere": false,
        "hasIncludeWhere": false,
        "hasIncludeRequired": false,
        "duplicating": true,
        "hasParentWhere": true,
        "hasParentRequired": false,
        "subQuery": false,
        "subQueryFilter": false,
        "where": {}
      }
    },
    "hasSingleAssociation": true,
    "hasMultiAssociation": true,
    "topLimit": 10,
    "hasDuplicating": false,
    "hasRequired": false,
    "hasWhere": true,
    "subQuery": false,
    "hasIncludeWhere": true,
    "hasIncludeRequired": true,
    "attributes": [
      "id",
      "url",
      "thumbnail_url",
      "width",
      "height",
      "order",
      "post_id"
    ],
    "separate": true,
    "required": false,
    "duplicating": false,
    "hasParentWhere": true,
    "hasParentRequired": false,
    "subQueryFilter": false,
    "where": {},
    "groupedLimit": {
      "limit": 9,
      "on": "[Circular]",
      "values": [
        "4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6",
        "336b8bd5-9c11-480a-828d-d517742bdbda",
        "7a76d7db-3ac9-4a25-b86f-10b8eebde4b1",
        "8507e97c-a576-4ec5-a203-6ca150be6761",
        "69357df6-5b0d-4b17-a076-0a371b9a6c45",
        "b53e93e7-b200-45e6-977b-2f37ff46612c",
        "9a054364-45a5-4d19-88f5-bb8ef80f73a4",
        "08f6b257-c66d-4390-8aa4-ee9cbaf27c01",
        "5c561b0d-56c9-4ca4-ba33-c02fe000c149"
      ]
    },
    "originalAttributes": "[Circular]",
    "tableNames": [
      "post_images"
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 info: Request completed: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775569355-1kwnd83g",
  "method": "GET",
  "url": "/api/users/me",
  "statusCode": 304,
  "duration": "43ms",
  "contentLength": 0
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 info: Request started: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775569401-dryoofom",
  "method": "GET",
  "url": "/api/users/me",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:49 debug: Executing (default): SELECT `id`, `username`, `nickname`, `phone`, `email`, `avatar`, `role`, `gender`, `bio`, `school`, `department`, `is_disabled`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt`, `deleted_at` AS `deletedAt` FROM `users` AS `User` WHERE (`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": false,
    "showWarnings": false,
    "where": "(`User`.`deleted_at` IS NULL AND `User`.`id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "rejectOnEmpty": false,
    "attributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      [
        "created_at",
        "createdAt"
      ],
      [
        "updated_at",
        "updatedAt"
      ],
      [
        "deleted_at",
        "deletedAt"
      ]
    ],
    "originalAttributes": [
      "id",
      "username",
      "nickname",
      "phone",
      "email",
      "avatar",
      "role",
      "gender",
      "bio",
      "school",
      "department",
      "is_disabled",
      "last_login_at",
      "createdAt",
      "updatedAt",
      "deletedAt"
    ],
    "tableNames": [
      "users"
    ],
    "type": "SELECT",
    "model": "[Circular]"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '4c259eaf-4fce-4c4e-98b7-dfa2a55ebce6' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `posts` AS `Post` WHERE (`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Post`.`deleted_at` IS NULL AND (`Post`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Post`.`status` = 'published'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published'))); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_type` = 'post' AND `Like`.`target_id` IN (SELECT id FROM posts WHERE user_id = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND status = 'published')))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`follower_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `follows` AS `Follow` WHERE (`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Follow`.`deleted_at` IS NULL AND `Follow`.`following_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '336b8bd5-9c11-480a-828d-d517742bdbda' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 info: Request started: GET /uploads/images/cdeace56-7b7b-40ae-b83e-10e90686a5af.png {
  "service": "campus-wall-api",
  "requestId": "1750775569426-0zc1rut0",
  "method": "GET",
  "url": "/uploads/images/cdeace56-7b7b-40ae-b83e-10e90686a5af.png",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
}
2025-06-24 22:32:49 info: Request completed: GET /api/users/me {
  "service": "campus-wall-api",
  "requestId": "1750775569401-dryoofom",
  "method": "GET",
  "url": "/api/users/me",
  "statusCode": 304,
  "duration": "27ms",
  "contentLength": 0
}
2025-06-24 22:32:49 info: Request completed: GET /uploads/images/cdeace56-7b7b-40ae-b83e-10e90686a5af.png {
  "service": "campus-wall-api",
  "requestId": "1750775569426-0zc1rut0",
  "method": "GET",
  "url": "/uploads/images/cdeace56-7b7b-40ae-b83e-10e90686a5af.png",
  "statusCode": 304,
  "duration": "2ms",
  "contentLength": 0
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '7a76d7db-3ac9-4a25-b86f-10b8eebde4b1' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '8507e97c-a576-4ec5-a203-6ca150be6761' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '69357df6-5b0d-4b17-a076-0a371b9a6c45' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = 'b53e93e7-b200-45e6-977b-2f37ff46612c' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '9a054364-45a5-4d19-88f5-bb8ef80f73a4' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '08f6b257-c66d-4390-8aa4-ee9cbaf27c01' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `likes` AS `Like` WHERE (`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Like`.`deleted_at` IS NULL AND (`Like`.`target_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Like`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba' AND `Like`.`target_type` = 'post'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 debug: Executing (default): SELECT count(*) AS `count` FROM `favorites` AS `Favorite` WHERE (`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba')); {
  "service": "campus-wall-api",
  "timing": {
    "plain": true,
    "raw": true,
    "showWarnings": false,
    "where": "(`Favorite`.`deleted_at` IS NULL AND (`Favorite`.`post_id` = '5c561b0d-56c9-4ca4-ba33-c02fe000c149' AND `Favorite`.`user_id` = '51d0ff31-82e1-4e6d-98f7-3aa8be2cd8ba'))",
    "hooks": true,
    "dataType": {
      "options": "[Circular]"
    },
    "includeIgnoreAttributes": false,
    "limit": null,
    "offset": null,
    "order": null,
    "attributes": [
      [
        {
          "fn": "count",
          "args": [
            {
              "col": "*"
            }
          ]
        },
        "count"
      ]
    ],
    "type": "SELECT"
  }
}
2025-06-24 22:32:49 info: Request completed: GET /api/posts/user/me?page=1&pageSize=10&type=published {
  "service": "campus-wall-api",
  "requestId": "1750775569359-z4n4ivrr",
  "method": "GET",
  "url": "/api/posts/user/me?page=1&pageSize=10&type=published",
  "statusCode": 304,
  "duration": "97ms",
  "contentLength": 0
}
