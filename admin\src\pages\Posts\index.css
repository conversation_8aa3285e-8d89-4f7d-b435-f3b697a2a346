/* 帖子管理页面样式 */
.posts-page {
  padding: 0;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  padding: 8px 0;
}

/* 帖子信息单元格 */
.post-info-cell {
  max-width: 280px;
}

.post-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.post-title {
  flex: 1;
  min-width: 0;
}

.pin-icon {
  color: #ff4d4f;
  margin-right: 4px;
}

.post-content {
  margin-bottom: 8px !important;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.post-images {
  display: flex;
  align-items: center;
  gap: 4px;
}

.post-image-thumb {
  border-radius: 4px;
  object-fit: cover;
}

.more-images {
  font-size: 12px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 帖子统计 */
.post-stats {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮样式 */
.posts-page .ant-btn-text {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.posts-page .ant-btn-text:hover {
  background: rgba(0, 0, 0, 0.04);
}

.posts-page .ant-btn-text.pinned {
  color: #ff4d4f;
}

/* 帖子详情模态框 */
.post-detail {
  padding: 16px 0;
}

.post-detail-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.post-detail-header h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.post-meta {
  margin-bottom: 8px;
}

.post-detail-content {
  margin-bottom: 24px;
  line-height: 1.6;
  color: #333;
}

.post-detail-images {
  margin-top: 16px;
}

.post-detail-stats {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  color: #666;
  font-size: 14px;
}

/* 表格样式优化 */
.posts-page .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

.posts-page .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.posts-page .ant-table-cell {
  padding: 12px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-form .ant-space {
    width: 100%;
    justify-content: stretch;
  }
  
  .search-form .ant-input,
  .search-form .ant-select {
    width: 100% !important;
  }
  
  .post-info-cell {
    max-width: none;
  }
  
  .post-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .posts-page .ant-table-cell {
    padding: 8px 12px;
  }
  
  .post-detail-images .ant-image {
    width: 100px !important;
  }
}
