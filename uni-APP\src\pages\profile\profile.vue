<template>
  <view class="profile-page">
    <!-- 顶部背景 -->
    <view class="profile-header">
      <view class="profile-cover">
        <image class="profile-bg" :src="userInfo.backgroundImage || '/static/images/common/profile-bg.jpg'" mode="aspectFill"></image>
        <view class="profile-overlay"></view>
        <view class="profile-gradient-overlay"></view>
      </view>
      
      <!-- 用户信息 -->
      <view class="profile-info">
        <view class="profile-avatar-container">
          <view class="profile-avatar-wrap">
            <image class="profile-avatar" :src="userInfo.avatar || '/static/images/common/default-avatar.png'" mode="aspectFill"></image>
            <view class="profile-avatar-border"></view>
            <view class="profile-avatar-glow"></view>
          </view>
        </view>
        
        <view class="profile-user-container">
          <view class="profile-user">
            <view class="profile-nickname">{{ userInfo.nickname || '游客' }}</view>
            <view class="profile-userid">ID: {{ userInfo.userId || '未登录' }}</view>
            <view class="profile-bio" v-if="userInfo.bio">{{ userInfo.bio }}</view>
          </view>
          
          <view class="profile-edit" @tap="editProfile">
            <text class="profile-edit-text">{{ userInfo.isLogin ? '编辑资料' : '登录/注册' }}</text>
            <view class="profile-edit-icon">
              <text class="iconfont icon-edit"></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 成就徽章 -->
      <view class="profile-achievements" v-if="userInfo.isLogin && userAchievements.length > 0">
        <view class="section-title">我的成就</view>
        <scroll-view scroll-x class="achievements-scroll" show-scrollbar="false">
          <view class="achievements-content">
            <view 
              v-for="(achievement, index) in userAchievements" 
              :key="index"
              class="achievement-item"
              :style="{animationDelay: index * 0.1 + 's'}"
            >
              <image class="achievement-icon" :src="achievement.icon"></image>
              <text class="achievement-name">{{ achievement.name }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 学校信息 -->
      <view class="profile-school" v-if="userInfo.isLogin && (userInfo.school || userInfo.department)">
        <view class="profile-school-content">
          <view class="profile-school-icon">
            <text class="iconfont icon-school"></text>
          </view>
          <view class="profile-school-info">
            <text class="profile-school-name" v-if="userInfo.school">{{ userInfo.school }}</text>
            <text class="profile-school-dept" v-if="userInfo.department">{{ userInfo.department }}</text>
          </view>
        </view>
      </view>
      
      <!-- 标签展示区 -->
      <view class="profile-tags" v-if="userInfo.isLogin && userInfo.tags && userInfo.tags.length > 0">
        <view class="section-title">个人标签</view>
        <scroll-view scroll-x class="tags-scroll" show-scrollbar="false">
          <view class="tags-container">
            <view 
              v-for="(tag, index) in userInfo.tags" 
              :key="index" 
              class="profile-tag"
              :style="{animationDelay: index * 0.05 + 's'}"
            >
              {{ tag }}
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 用户数据 -->
      <view class="profile-stats-container">
        <view class="profile-stats">
          <view class="profile-stat" @tap="handleTabClick('post')">
            <view class="profile-stat-number">{{ userInfo.postCount || 0 }}</view>
            <view class="profile-stat-label">帖子</view>
          </view>

          <view class="profile-stat" @tap="handleTabClick('favorite')">
            <view class="profile-stat-number">{{ userInfo.favoriteCount || 0 }}</view>
            <view class="profile-stat-label">收藏</view>
          </view>

          <view class="profile-stat" @tap="goToFollowList('following')">
            <view class="profile-stat-number">{{ userInfo.followingCount || 0 }}</view>
            <view class="profile-stat-label">关注</view>
          </view>

          <view class="profile-stat" @tap="goToFollowList('followers')">
            <view class="profile-stat-number">{{ userInfo.followersCount || 0 }}</view>
            <view class="profile-stat-label">粉丝</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 内容区 -->
    <view class="profile-content">
      <!-- 标签页 -->
      <view class="profile-tabs">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index" 
          :class="['profile-tab', { 'active': currentTab === tab.key }]"
          @tap="handleTabClick(tab.key)"
        >
          <text class="tab-text">{{ tab.name }}</text>
          <view class="tab-indicator" v-if="currentTab === tab.key"></view>
        </view>
      </view>
      
      <!-- 内容区 -->
      <swiper 
        class="profile-swiper" 
        :current="tabIndex" 
        @change="handleSwiperChange"
        :duration="300"
      >
        <!-- 帖子页 -->
        <swiper-item class="profile-swiper-item">
          <scroll-view 
            scroll-y 
            class="profile-scroll" 
            @scrolltolower="loadMore('post')" 
            refresher-enabled 
            :refresher-triggered="postRefreshing" 
            @refresherrefresh="refreshPosts"
          >
            <view class="profile-posts" v-if="posts.length > 0">
              <view class="post-list">
                <post-card 
                  v-for="(post, index) in posts" 
                  :key="post.id" 
                  :post="post"
                  :style="{ animationDelay: index * 0.1 + 's' }"
                  class="post-card-item"
                ></post-card>
              </view>
              <view class="load-more" v-if="postHasMore">
                <text class="load-more-text">正在加载更多...</text>
              </view>
              <view class="no-more" v-else>
                <text class="no-more-text">没有更多了</text>
              </view>
            </view>
            <view class="empty-container" v-else>
              <image class="empty-image" src="/static/images/common/empty-posts.png" mode="aspectFit"></image>
              <text class="empty-text">{{ postLoading ? '加载中...' : '暂无帖子' }}</text>
              <view class="create-post" @tap="createPost" v-if="!postLoading">
                <text class="create-post-text">发布帖子</text>
              </view>
            </view>
          </scroll-view>
        </swiper-item>
        
        <!-- 收藏页 -->
        <swiper-item class="profile-swiper-item">
          <scroll-view
            scroll-y
            class="profile-scroll"
            @scrolltolower="loadMore('favorite')"
            refresher-enabled
            :refresher-triggered="likeRefreshing"
            @refresherrefresh="refreshLikes"
          >
            <view class="profile-likes" v-if="likes.length > 0">
              <view class="post-list">
                <post-card 
                  v-for="(like, index) in likes" 
                  :key="like.id" 
                  :post="like.post"
                  :style="{ animationDelay: index * 0.1 + 's' }"
                  class="post-card-item"
                ></post-card>
              </view>
              <view class="load-more" v-if="likeHasMore">
                <text class="load-more-text">正在加载更多...</text>
              </view>
              <view class="no-more" v-else>
                <text class="no-more-text">没有更多了</text>
              </view>
            </view>
            <view class="empty-container" v-else>
              <image class="empty-image" src="/static/images/common/empty-likes.png" mode="aspectFit"></image>
              <text class="empty-text">{{ likeLoading ? '加载中...' : '暂无收藏内容' }}</text>
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 设置入口 -->
    <view class="profile-settings" @tap="goSettings">
      <app-icon name="more" size="md" color="#fff"></app-icon>
    </view>
    
    <!-- 底部安全区占位 -->
    <view class="safe-area"></view>
  </view>
</template>

<script>
import PostList from '@/components/post/PostList.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import { UrlUtils } from '@/utils';
import PostCard from '@/components/post/PostCard.vue';
// 导入API
import api from '@/api';

export default {
  components: {
    PostList,
    AppIcon,
    PostCard
  },
  data() {
    return {
      userInfo: {
        isLogin: false,
        avatar: '',
        nickname: '',
        userId: '',
        bio: '',
        school: '',
        department: '',
        backgroundImage: '',
        postCount: 0,
        likeCount: 0,
        favoriteCount: 0,
        followingCount: 0,
        followersCount: 0,
        tags: []
      },
      userAchievements: [], // 改为空数组，从API获取
      tabs: [
        { key: 'post', name: '我的帖子' },
        { key: 'favorite', name: '我的收藏' }
      ],
      currentTab: 'post',
      tabIndex: 0,
      
      // 帖子数据
      posts: [],
      postPage: 1,
      postPageSize: 10,
      postHasMore: true,
      postRefreshing: false,
      postLoading: false,
      
      // 收藏数据
      likes: [],
      likePage: 1,
      likePageSize: 10,
      likeHasMore: true,
      likeRefreshing: false,
      likeLoading: false,
      
      // 原始数据（保持兼容性）
      activeTab: 'post',
      postList: [],
      favoriteList: [],
      draftList: [],
      loading: false,
      loadingFav: false,
      loadingDraft: false,
      refreshing: false,
      finished: false,
      finishedFav: false,
      finishedDraft: false,
      page: 1,
      pageSize: 10
    };
  },
  computed: {
    isLogin() {
      return this.userInfo.isLogin;
    },
    
    userId() {
      return this.userInfo?.userId || '';
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.loadPosts();
  },
  onShow() {
    // 页面显示时刷新数据
    this.loadUserInfo();
    this.refreshCurrentTab();
  },
  onPullDownRefresh() {
    this.refreshCurrentTab();
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      // 检查token是否存在
      const token = uni.getStorageSync('token');
      if (!token) {
        this.userInfo = {
          isLogin: false,
          avatar: '',
          nickname: '游客',
          userId: '',
          bio: '登录后查看个人资料'
        };
        return;
      }
      
      // API请求获取用户信息
      api.user.getInfo().then(res => {
        console.log('获取用户信息成功:', res);
        
        if (res.code === 0 || res.code === 200) {
          const userData = res.data;
          
          // 根据API文档，统计数据在stats对象中
          const stats = userData.stats || {};

          this.userInfo = {
            isLogin: true,
            avatar: userData.avatar,
            nickname: userData.nickname || userData.username,
            userId: userData.id,
            bio: userData.bio || '这个人很懒，还没有填写个人简介',
            school: userData.school || '',
            department: userData.department || '',
            backgroundImage: userData.backgroundImage || '',
            // 根据API文档映射字段名
            postCount: stats.postCount || 0,
            likeCount: stats.likeCount || 0,
            favoriteCount: stats.favoriteCount || 0,
            followingCount: stats.followCount || 0,  // API中是followCount
            followersCount: stats.fansCount || 0,    // API中是fansCount
            tags: userData.tags || []
          };
          
          // 获取用户成就
          if (userData.achievements) {
            this.userAchievements = userData.achievements;
          }
        } else {
          // 登录状态失效
          this.userInfo.isLogin = false;
          this.userInfo.nickname = '游客';
          uni.removeStorageSync('token');
        }
      }).catch(err => {
        console.error('获取用户信息失败:', err);
        this.userInfo.isLogin = false;
        this.userInfo.nickname = '游客';
      });
    },
    
    // 编辑个人资料
    editProfile() {
      if (!this.userInfo.isLogin) {
        uni.navigateTo({
          url: '/pages/auth/login'
        });
        return;
      }
      
      uni.navigateTo({
        url: '/pages/profile/edit'
      });
    },
    
    // Tab切换处理
    handleTabClick(tab) {
      // 如果点击的是统计数据，切换到对应的标签页
      if (tab === 'favorite') {
        this.currentTab = 'favorite';
        this.tabIndex = this.tabs.findIndex(t => t.key === 'favorite');
        this.refreshCurrentTab();
        return;
      }

      this.currentTab = tab;
      this.tabIndex = this.tabs.findIndex(t => t.key === tab);
      this.refreshCurrentTab();

      // 兼容原始功能
      this.activeTab = tab === 'post' ? 'post' : 'favorite';
      this.changeTab(this.activeTab);
    },

    // 跳转到关注/粉丝列表
    goToFollowList(type) {
      if (!this.userInfo.isLogin) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      uni.navigateTo({
        url: `/pages/profile/follow?type=${type}&userId=${this.userInfo.userId}`
      });
    },
    
    // 滑动切换处理
    handleSwiperChange(e) {
      const index = e.detail.current;
      this.tabIndex = index;
      this.currentTab = this.tabs[index].key;
      this.refreshCurrentTab();
      
      // 兼容原始功能
      this.activeTab = this.currentTab === 'post' ? 'post' : 'like';
    },
    
    // 刷新当前标签页数据
    refreshCurrentTab() {
      switch (this.currentTab) {
        case 'post':
          this.refreshPosts();
          break;
        case 'favorite':
          this.refreshLikes();
          break;
      }
    },
    
    // 加载更多
    loadMore(type) {
      if (type === 'post') {
        this.loadMorePosts();
      } else if (type === 'favorite') {
        this.loadMoreLikes();
      }
    },
    
    // 帖子相关方法
    refreshPosts() {
      this.postRefreshing = true;
      this.postPage = 1;
      this.loadPosts();
      
      // 兼容原始功能
      this.refreshing = true;
      this.page = 1;
      this.handleScrollRefresh();
    },
    
    loadPosts() {
      if (this.postLoading) return;
      
      this.postLoading = true;
      
      // 调用API获取用户发布的帖子
      api.user.getPosts(this.postPage, this.postPageSize, 'published').then(res => {
        console.log('获取用户帖子成功:', res);
        
        if (res.code === 0 || res.code === 200) {
          const postsData = res.data.list || res.data.items || [];
          const total = res.data.total || 0;
          
          if (this.postPage === 1) {
            this.posts = postsData;
          } else {
            this.posts = [...this.posts, ...postsData];
          }
          
          this.postHasMore = this.posts.length < total;
          
          // 兼容原始数据
          this.postList = this.posts;
        } else {
          uni.showToast({
            title: res.msg || '获取帖子失败',
            icon: 'none'
          });
        }
        
        this.postLoading = false;
        this.postRefreshing = false;
        
        // 兼容原始功能
        this.loading = false;
        this.refreshing = false;
        this.finished = !this.postHasMore;
      }).catch(err => {
        console.error('获取用户帖子失败:', err);
        this.postLoading = false;
        this.postRefreshing = false;
        
        // 兼容原始功能
        this.loading = false;
        this.refreshing = false;
        
        uni.showToast({
          title: '获取帖子失败',
          icon: 'none'
        });
      });
    },
    
    loadMorePosts() {
      if (this.postLoading || !this.postHasMore) return;
      
      this.postPage++;
      this.loadPosts();
      
      // 兼容原始功能
      this.page++;
    },
    
    // 收藏相关方法
    refreshLikes() {
      this.likeRefreshing = true;
      this.likePage = 1;
      this.loadLikes();
    },
    
    loadLikes() {
      if (this.likeLoading) return;
      
      this.likeLoading = true;
      
      // 调用API获取用户收藏
      api.user.getFavorites(this.likePage, this.likePageSize).then(res => {
        console.log('获取用户收藏成功:', res);
        
        if (res.code === 0 || res.code === 200) {
          const favoritesData = res.data.list || res.data.items || [];
          const total = res.data.total || 0;
          
          if (this.likePage === 1) {
            this.likes = favoritesData;
          } else {
            this.likes = [...this.likes, ...favoritesData];
          }
          
          this.likeHasMore = this.likes.length < total;
          
          // 兼容原始数据 
          this.favoriteList = this.likes;
        } else {
          uni.showToast({
            title: res.msg || '获取收藏失败',
            icon: 'none'
          });
        }
        
        this.likeLoading = false;
        this.likeRefreshing = false;
        
        // 兼容原始功能
        this.loadingFav = false;
        this.finishedFav = !this.likeHasMore;
      }).catch(err => {
        console.error('获取用户收藏失败:', err);
        this.likeLoading = false;
        this.likeRefreshing = false;
        
        // 兼容原始功能
        this.loadingFav = false;
        
        uni.showToast({
          title: '获取收藏失败',
          icon: 'none'
        });
      });
    },
    
    loadMoreLikes() {
      if (this.likeLoading || !this.likeHasMore) return;
      
      this.likePage++;
      this.loadLikes();
    },
    
    // 工具方法
    formatTime(time) {
      // 将时间戳格式化为友好的时间显示
      if (!time) return '';
      
      const now = new Date().getTime();
      const diff = now - new Date(time).getTime();
      
      if (diff < 60 * 1000) {
        return '刚刚';
      } else if (diff < 60 * 60 * 1000) {
        return Math.floor(diff / (60 * 1000)) + '分钟前';
      } else if (diff < 24 * 60 * 60 * 1000) {
        return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
      } else if (diff < 30 * 24 * 60 * 60 * 1000) {
        return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
      } else {
        const date = new Date(time);
        return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
      }
    },
    
    // 跳转帖子详情页
    navigateToPost(postId) {
      uni.navigateTo({
        url: `/pages/post/detail?id=${postId}`
      });
    },
    
    // 发布帖子
    createPost() {
      uni.navigateTo({
        url: '/pages/post/create'
      });
    },
    
    // 发现用户
    discoverUsers() {
      uni.navigateTo({
        url: '/pages/user/discover'
      });
    },
    
    // 关注/取消关注用户
    toggleFollow(user) {
      if (!this.isLogin) {
        uni.navigateTo({
          url: '/pages/auth/login'
        });
        return;
      }
      
      user.isFollowed = !user.isFollowed;
      
      // 模拟API请求
      uni.showToast({
        title: user.isFollowed ? '已关注' : '已取消关注',
        icon: 'none'
      });
    },
    
    // 保留原有方法以保持兼容性
    changeTab(tab) {
      this.activeTab = tab;
      
      if (tab === 'post' && this.postList.length === 0) {
        this.loadPosts();
      } else if (tab === 'like' && this.favoriteList.length === 0) {
        this.loadLikes();
      }
    },
    
    getSwiperIndex() {
      if (this.activeTab === 'post') return 0;
      if (this.activeTab === 'like') return 1;
      return 0;
    },
    
    handleScrollRefresh() {
      this.refreshing = true;
      this.page = 1;
      
      if (this.activeTab === 'post') {
        this.loadPosts();
      } else if (this.activeTab === 'like') {
        this.loadLikes();
      }
    },
    
    loadFavorites() {
      // 已在新代码中实现为loadLikes
      this.loadLikes();
    },
    
    loadMoreFavorites() {
      // 已在新代码中实现为loadMoreLikes
      this.loadMoreLikes();
    },
    
    handleLike() {
      uni.showToast({
        title: '点赞成功',
        icon: 'none'
      });
    },
    
    handleComment(post) {
      uni.navigateTo({
        url: `/pages/post/detail?id=${post.id}&focus=comment`
      });
    },
    
    handleFavorite() {
      uni.showToast({
        title: '收藏成功',
        icon: 'none'
      });
    },
    
    handleShare() {
      uni.showShareMenu({
        withShareTicket: true
      });
    },
    
    handleEdit(post) {
      uni.navigateTo({
        url: `/pages/post/edit?id=${post.id}`
      });
    },
    
    handleDelete() {
      uni.showModal({
        title: '提示',
        content: '确定要删除这条帖子吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'none'
            });
          }
        }
      });
    },
    
    goLogin() {
      uni.navigateTo({
        url: '/pages/auth/login'
      });
    },
    
    goSettings() {
      uni.navigateTo({
        url: '/pages/settings/index'
      });
    },

    // 创建帖子
    createPost() {
      uni.switchTab({
        url: '/pages/publish/publish'
      });
    },

    // 兼容原有方法
    changeTab(tab) {
      // 兼容原有的tab切换逻辑
      console.log('切换到标签页:', tab);
    },

    handleScrollRefresh() {
      // 兼容原有的下拉刷新逻辑
      this.refreshCurrentTab();
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
@import '@/styles/mixins.scss';

/* 页面容器 */
.profile-page {
  min-height: 100vh;
  background-color: $bg-page;
  position: relative;
}

/* 顶部区域 */
.profile-header {
  position: relative;
  background-color: #fff;
  border-radius: 0 0 40rpx 40rpx;
  overflow: hidden;
  box-shadow: $shadow-sm;
  margin-bottom: 20rpx;
  padding-bottom: 30rpx;
}

/* 封面背景 */
.profile-cover {
  position: relative;
  height: 360rpx;
  overflow: hidden;
}

.profile-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.profile-gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 20%) 100%);
  opacity: 0.7;
  z-index: 2;
}

/* 用户信息 */
.profile-info {
  @include flex(row, flex-start, center);
  padding: 30rpx;
  margin-top: -80rpx;
  position: relative;
  z-index: 10;
}

.profile-avatar-container {
  position: relative;
  margin-right: $spacing-md;
  width: 170rpx;
  height: 170rpx;
}

.profile-avatar-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: $radius-circle;
  overflow: hidden;
  box-shadow: $shadow-lg;
}

.profile-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar-border {
  position: absolute;
  top: -5rpx;
  left: -5rpx;
  right: -5rpx;
  bottom: -5rpx;
  border-radius: $radius-circle;
  border: 5rpx solid rgba(255, 255, 255, 0.8);
  z-index: 3;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.02); opacity: 0.6; }
  100% { transform: scale(1); opacity: 0.8; }
}

.profile-avatar-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: $radius-circle;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  opacity: 0.8;
  z-index: 2;
  animation: glow 2s infinite ease-in-out;
}

@keyframes glow {
  0% { opacity: 0.8; }
  50% { opacity: 0.6; }
  100% { opacity: 0.8; }
}

.profile-user-container {
  flex: 1;
  @include flex(column, space-between, flex-start);
}

.profile-user {
  @include flex(column, center, flex-start);
}

.profile-nickname {
  font-size: 40rpx;
  color: $text-primary;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.profile-userid {
  font-size: $font-size-sm;
  color: $text-secondary;
  margin-bottom: 12rpx;
}

.profile-bio {
  font-size: $font-size-sm;
  color: $text-secondary;
  margin-top: 8rpx;
  line-height: 1.5;
  max-width: 100%;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 编辑按钮 */
.profile-edit {
  @include flex(row, center, center);
  background: linear-gradient(to right, $primary-color, lighten($primary-color, 10%));
  border-radius: $radius-lg;
  padding: 12rpx 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 6rpx 15rpx rgba($primary-color, 0.3);
  transition: all 0.3s;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 3rpx 8rpx rgba($primary-color, 0.2);
  }
}

.profile-edit-text {
  color: #fff;
  font-size: $font-size-md;
  font-weight: 500;
}

.profile-edit-icon {
  margin-left: 12rpx;
  font-size: 32rpx;
  color: #ffffff;
}

/* 学校信息 */
.profile-school {
  padding: 0 30rpx;
  margin-top: 20rpx;
}

.profile-school-content {
  @include flex(row, flex-start, center);
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: $radius-lg;
  padding: 16rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.profile-school-icon {
  color: $primary-color;
  font-size: 36rpx;
  margin-right: 16rpx;
}

.profile-school-info {
  @include flex(column, center, flex-start);
}

.profile-school-name {
  font-size: $font-size-md;
  color: $text-primary;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.profile-school-dept {
  font-size: $font-size-sm;
  color: $text-secondary;
}

/* 统计信息 */
.profile-stats-container {
  padding: 0 30rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.profile-stats {
  @include flex(row, space-around, center);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: $radius-lg;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.profile-stat {
  @include flex(column, center, center);
  flex: 1;
  cursor: pointer;
  transition: all 0.3s;
  padding: 10rpx;
  border-radius: $radius-md;

  &:active {
    transform: scale(0.95);
    background: rgba($primary-color, 0.1);
  }
}

.profile-stat-number {
  font-size: 44rpx;
  font-weight: bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  line-height: 1;
}

.profile-stat-label {
  font-size: $font-size-sm;
  color: $text-secondary;
  font-weight: 500;
}

/* 成就徽章 */
.profile-achievements {
  padding: 20rpx 30rpx;
}

.section-title {
  font-size: $font-size-md;
  font-weight: bold;
  color: $text-primary;
  margin-bottom: 16rpx;
}

.achievements-scroll {
  overflow-x: auto;
  padding: 10rpx 0;
  width: 100%;
}

.achievements-content {
  @include flex(row, flex-start, center);
}

.achievement-item {
  @include flex(column, center, center);
  background-color: #fff;
  border-radius: $radius-lg;
  padding: 16rpx;
  margin-right: 20rpx;
  width: 120rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.achievement-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.achievement-name {
  font-size: $font-size-sm;
  color: $text-primary;
  text-align: center;
  line-height: 1.3;
  height: 32rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 标签 */
.profile-tags {
  padding: 20rpx 30rpx;
}

.tags-scroll {
  overflow-x: auto;
  padding: 10rpx 0;
  width: 100%;
}

.tags-container {
  @include flex(row, flex-start, center);
  flex-wrap: nowrap;
}

.profile-tag {
  background-color: rgba($primary-color, 0.1);
  color: $primary-color;
  font-size: $font-size-sm;
  border-radius: $radius-md;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  white-space: nowrap;
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

/* 内容区 */
.profile-content {
  flex: 1;
  background-color: #fff;
  border-radius: 40rpx 40rpx 0 0;
  overflow: hidden;
  margin-top: 20rpx;
}

/* 标签页 */
.profile-tabs {
  @include flex(row, space-around, center);
  background-color: #fff;
  border-bottom: 2rpx solid $border-color;
  position: relative;
  z-index: 10;
}

.profile-tab {
  @include flex(column, center, center);
  position: relative;
  padding: 24rpx 0;
  flex: 1;
  transition: all 0.3s;
  
  &.active {
    color: $primary-color;
    font-weight: 500;
  }
  
  &:active {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.tab-text {
  font-size: $font-size-md;
  transition: all 0.3s;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: $primary-color;
  border-radius: $radius-sm;
  transition: all 0.3s;
  animation: fadeIn 0.3s;
}

/* 滑动区域 */
.profile-swiper {
  height: calc(100vh - 520rpx);
  width: 100%;
}

.profile-swiper-item {
  height: 100%;
  overflow: hidden;
}

.profile-scroll {
  height: 100%;
}

/* 帖子列表 */
.profile-posts {
  padding: 20rpx;
}

.post-list {
  @include flex(column, flex-start, stretch);
}

.post-card-item {
  margin-bottom: 30rpx;
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

/* 收藏列表 */
.profile-likes {
  padding: 20rpx;
}

/* 加载更多 */
.load-more {
  @include flex(row, center, center);
  padding: 20rpx 0;
}

.load-more-text {
  font-size: $font-size-sm;
  color: $text-tertiary;
}

.no-more {
  @include flex(row, center, center);
  padding: 20rpx 0;
}

.no-more-text {
  font-size: $font-size-sm;
  color: $text-tertiary;
}

/* 空状态 */
.empty-container {
  @include flex(column, center, center);
  padding: 80rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: $font-size-md;
  color: $text-tertiary;
  margin-bottom: 30rpx;
}

.create-post, .discover-users {
  @include flex(row, center, center);
  background: linear-gradient(to right, $primary-color, lighten($primary-color, 10%));
  border-radius: $radius-md;
  padding: 16rpx 40rpx;
  box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.2);
  transition: all 0.3s;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 6rpx rgba($primary-color, 0.1);
  }
}

.create-post-text, .discover-users-text {
  font-size: $font-size-md;
  color: #fff;
  font-weight: 500;
}
</style> 