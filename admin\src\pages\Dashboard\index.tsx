import React, { useEffect, useState } from 'react'
import { Card, Row, Col, Statistic, Tag, List, Avatar, Space, Button } from 'antd'
import {
  UserOutlined,
  FileTextOutlined,
  MessageOutlined,
  EyeOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  HeartOutlined,
  StarOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Area,
  AreaChart,
} from 'recharts'
import { formatNumber, formatTime } from '@/utils'
import { dashboardService } from '@/services/dashboard'
import type { DashboardStats, ChartData, TopPost, TopUser } from '@/services/dashboard'
import './index.css'

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [statsLoading, setStatsLoading] = useState(false)
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null)
  const [chartData, setChartData] = useState<ChartData[]>([])
  const [topPosts, setTopPosts] = useState<TopPost[]>([])
  const [topUsers, setTopUsers] = useState<TopUser[]>([])
  const [realTimeData, setRealTimeData] = useState({
    onlineUsers: 0,
    todayVisits: 0,
    todayPosts: 0,
    todayComments: 0,
  })

  // 图表颜色配置
  const COLORS = ['#2b85e4', '#52c41a', '#faad14', '#f5222d', '#722ed1']

  // 加载所有数据
  const loadDashboardData = async () => {
    setLoading(true)
    try {
      const [stats, chart, posts, users, realTime] = await Promise.all([
        dashboardService.getStats(),
        dashboardService.getChartData(7),
        dashboardService.getTopPosts(5),
        dashboardService.getTopUsers(5),
        dashboardService.getRealTimeData(),
      ])

      setDashboardStats(stats)
      setChartData(chart)
      setTopPosts(posts)
      setTopUsers(users)
      setRealTimeData(realTime)
    } catch (error) {
      console.error('加载仪表板数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 刷新统计数据
  const refreshStats = async () => {
    setStatsLoading(true)
    try {
      const [stats, realTime] = await Promise.all([
        dashboardService.getStats(),
        dashboardService.getRealTimeData(),
      ])
      setDashboardStats(stats)
      setRealTimeData(realTime)
    } catch (error) {
      console.error('刷新数据失败:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  // 获取趋势指示器
  const getTrendIndicator = (value: number) => {
    if (value > 0) {
      return (
        <span className="trend-up">
          <ArrowUpOutlined /> {value.toFixed(1)}%
        </span>
      )
    } else if (value < 0) {
      return (
        <span className="trend-down">
          <ArrowDownOutlined /> {Math.abs(value).toFixed(1)}%
        </span>
      )
    }
    return <span className="trend-neutral">0%</span>
  }

  useEffect(() => {
    loadDashboardData()

    // 设置定时刷新实时数据
    const interval = setInterval(() => {
      dashboardService.getRealTimeData().then(setRealTimeData)
    }, 30000) // 30秒刷新一次

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="dashboard">
      {/* 实时数据和刷新按钮 */}
      <Card className="realtime-card mb-16">
        <div className="realtime-header">
          <h3>实时数据</h3>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshStats}
            loading={statsLoading}
            size="small"
          >
            刷新
          </Button>
        </div>
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={6}>
            <div className="realtime-item">
              <div className="realtime-value">{realTimeData.onlineUsers}</div>
              <div className="realtime-label">在线用户</div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className="realtime-item">
              <div className="realtime-value">{formatNumber(realTimeData.todayVisits)}</div>
              <div className="realtime-label">今日访问</div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className="realtime-item">
              <div className="realtime-value">{realTimeData.todayPosts}</div>
              <div className="realtime-label">今日发帖</div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className="realtime-item">
              <div className="realtime-value">{realTimeData.todayComments}</div>
              <div className="realtime-label">今日评论</div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={dashboardStats?.userStats.total || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={getTrendIndicator(dashboardStats?.userStats.weeklyGrowth || 0)}
              loading={loading}
            />
            <div className="stat-detail">
              今日新增: {dashboardStats?.userStats.todayNew || 0}
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总帖子数"
              value={dashboardStats?.postStats.total || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={getTrendIndicator(dashboardStats?.postStats.weeklyGrowth || 0)}
              loading={loading}
            />
            <div className="stat-detail">
              今日新增: {dashboardStats?.postStats.todayNew || 0}
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总评论数"
              value={dashboardStats?.commentStats.total || 0}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#722ed1' }}
              suffix={getTrendIndicator(dashboardStats?.commentStats.weeklyGrowth || 0)}
              loading={loading}
            />
            <div className="stat-detail">
              今日新增: {dashboardStats?.commentStats.todayNew || 0}
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日活跃"
              value={dashboardStats?.userStats.todayActive || 0}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#fa541c' }}
              suffix={
                <span className="engagement-score">
                  互动: {dashboardStats?.engagementStats.avgEngagement.toFixed(1) || 0}
                </span>
              }
              loading={loading}
            />
            <div className="stat-detail">
              总点赞: {formatNumber(dashboardStats?.engagementStats.totalLikes || 0)}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} lg={12}>
          <Card title="活跃度趋势" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey="users"
                  stackId="1"
                  stroke="#2b85e4"
                  fill="#2b85e4"
                  fillOpacity={0.6}
                  name="活跃用户"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="内容统计" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="posts" fill="#52c41a" name="发布帖子" />
                <Bar dataKey="comments" fill="#faad14" name="评论数" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 热门内容和活跃用户 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} lg={12}>
          <Card title="热门帖子" loading={loading}>
            <List
              dataSource={topPosts}
              renderItem={(post, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div className="rank-badge">
                        {index + 1}
                      </div>
                    }
                    title={
                      <div className="post-title-row">
                        <span className="post-title">{post.title}</span>
                        <Tag>{post.author}</Tag>
                      </div>
                    }
                    description={
                      <Space size="large">
                        <span><EyeOutlined /> {formatNumber(post.viewCount)}</span>
                        <span><HeartOutlined /> {formatNumber(post.likeCount)}</span>
                        <span><MessageOutlined /> {formatNumber(post.commentCount)}</span>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="活跃用户" loading={loading}>
            <List
              dataSource={topUsers}
              renderItem={(user, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div className="rank-badge">
                        {index + 1}
                      </div>
                    }
                    title={
                      <div className="user-title-row">
                        <Avatar size="small" src={user.avatar} icon={<UserOutlined />} />
                        <span className="user-name">{user.username}</span>
                      </div>
                    }
                    description={
                      <Space size="large">
                        <span><FileTextOutlined /> {user.postCount}帖</span>
                        <span><HeartOutlined /> {formatNumber(user.likeCount)}</span>
                        <span><UserOutlined /> {user.followerCount}粉丝</span>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

    </div>
  )
}

export default Dashboard
