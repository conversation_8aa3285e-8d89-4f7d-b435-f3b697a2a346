import React, { useEffect, useState } from 'react'
import { Card, Row, Col, Statistic, Table, Tag, Progress } from 'antd'
import {
  UserOutlined,
  FileTextOutlined,
  MessageOutlined,
  EyeOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'
import { formatNumber, formatTime } from '@/utils'
import type { Statistics } from '@/types'
import './index.css'

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [statistics, setStatistics] = useState<Statistics>({
    userCount: 0,
    postCount: 0,
    commentCount: 0,
    todayActiveUsers: 0,
    todayNewUsers: 0,
    todayNewPosts: 0,
  })

  // 模拟数据
  const chartData = [
    { name: '周一', users: 120, posts: 45 },
    { name: '周二', users: 132, posts: 52 },
    { name: '周三', users: 101, posts: 38 },
    { name: '周四', users: 134, posts: 48 },
    { name: '周五', users: 90, posts: 35 },
    { name: '周六', users: 230, posts: 78 },
    { name: '周日', users: 210, posts: 65 },
  ]

  const recentPosts = [
    {
      id: '1',
      title: '校园生活分享',
      author: '张三',
      category: '生活',
      status: 'published',
      createdAt: '2024-01-15 10:30:00',
      viewCount: 156,
    },
    {
      id: '2',
      title: '学习经验交流',
      author: '李四',
      category: '学习',
      status: 'published',
      createdAt: '2024-01-15 09:15:00',
      viewCount: 89,
    },
    {
      id: '3',
      title: '社团活动通知',
      author: '王五',
      category: '活动',
      status: 'published',
      createdAt: '2024-01-15 08:45:00',
      viewCount: 234,
    },
  ]

  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      width: 100,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 80,
      render: (category: string) => (
        <Tag color="blue">{category}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'published' ? 'green' : 'orange'}>
          {status === 'published' ? '已发布' : '草稿'}
        </Tag>
      ),
    },
    {
      title: '浏览量',
      dataIndex: 'viewCount',
      key: 'viewCount',
      width: 80,
      render: (count: number) => formatNumber(count),
    },
    {
      title: '发布时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time: string) => formatTime(time, 'MM-DD HH:mm'),
    },
  ]

  useEffect(() => {
    // 模拟加载数据
    setTimeout(() => {
      setStatistics({
        userCount: 1234,
        postCount: 567,
        commentCount: 890,
        todayActiveUsers: 89,
        todayNewUsers: 12,
        todayNewPosts: 23,
      })
      setLoading(false)
    }, 1000)
  }, [])

  return (
    <div className="dashboard">
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={statistics.userCount}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={
                <span className="trend-up">
                  <ArrowUpOutlined /> 12%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总帖子数"
              value={statistics.postCount}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <span className="trend-up">
                  <ArrowUpOutlined /> 8%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总评论数"
              value={statistics.commentCount}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#722ed1' }}
              suffix={
                <span className="trend-down">
                  <ArrowDownOutlined /> 3%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日活跃"
              value={statistics.todayActiveUsers}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#fa541c' }}
              suffix={
                <span className="trend-up">
                  <ArrowUpOutlined /> 15%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="mb-24">
        <Col xs={24} lg={16}>
          <Card title="用户活跃度趋势" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="users"
                  stroke="#2b85e4"
                  strokeWidth={2}
                  name="活跃用户"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="内容发布统计" loading={loading}>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="posts" fill="#52c41a" name="发布帖子" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 最新帖子 */}
      <Card title="最新帖子" loading={loading}>
        <Table
          columns={columns}
          dataSource={recentPosts}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  )
}

export default Dashboard
