{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --config vite.config.js", "build": "vite build --config vite.config.js", "preview": "vite preview --config vite.config.js"}, "dependencies": {"axios": "^1.9.0", "echarts": "^5.4.3", "element-plus": "^2.9.10", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.5"}}