# 校园墙项目 API 对接说明

## 项目概述

本项目是一个校园墙社交平台，包含三个部分：
- 前端：基于 uni-app 的跨平台应用
- 后端：基于 Node.js 的 RESTful API 服务
- 管理端：基于 Vue 的管理员后台

本文档主要说明前端与后端 API 的对接方法和实现细节。

## API 基础信息

- 基础URL: `http://localhost:12346/api`
- 认证方式: Bearer Token (JWT)
- 响应格式: JSON

## 对接实现

### 1. 工具类

#### 1.1 API 工具类 (api.js)

用于封装所有 API 请求，提供统一的接口调用方法。主要功能：
- 请求拦截：自动添加 token 到请求头
- 响应拦截：统一处理错误，包括 401 未授权
- 按功能模块分类的 API 封装

#### 1.2 配置工具类 (config.js)

存储全局配置信息，包括：
- API 基础 URL
- 默认资源路径
- 请求超时设置
- Token 过期时间

#### 1.3 验证工具类 (validator.js)

提供表单验证功能，支持：
- 必填字段验证
- 字符串长度验证
- 字段一致性验证
- 用户名格式验证
- 密码强度验证
- 邮箱格式验证

#### 1.4 状态管理工具 (store.js)

提供简单的全局状态管理，包括：
- 用户信息和登录状态
- 帖子数据缓存
- 热门话题数据
- 用户和话题数据缓存

### 2. 页面对接

#### 2.1 登录页面 (login.vue)

- 集成表单验证
- 调用登录 API
- 保存 token 和用户信息
- 登录成功后获取用户详细信息
- 支持"记住我"功能

#### 2.2 注册页面 (register.vue)

- 集成表单验证
- 调用注册 API
- 注册成功后获取用户详细信息
- 跳转到首页

#### 2.3 应用初始化 (App.vue)

- 检查登录状态
- 获取用户信息
- 执行API健康检查

### 3. API 模块划分

API 接口按功能模块划分为：

1. **用户认证**
   - 注册
   - 登录
   - 获取当前用户信息
   - 更新用户信息

2. **帖子相关**
   - 获取帖子列表
   - 获取帖子详情
   - 创建帖子
   - 更新帖子
   - 删除帖子

3. **评论相关**
   - 获取评论列表
   - 添加评论
   - 删除评论
   - 点赞评论
   - 取消评论点赞

4. **点赞/收藏相关**
   - 点赞/取消点赞帖子
   - 收藏/取消收藏帖子
   - 获取用户点赞的帖子
   - 获取用户收藏的帖子

5. **用户关系相关**
   - 关注/取消关注用户
   - 获取关注列表
   - 获取粉丝列表

6. **话题和搜索相关**
   - 获取热门话题
   - 获取话题列表/详情
   - 获取话题下的帖子
   - 全局搜索和帖子搜索

7. **系统相关**
   - 健康检查

## 认证流程

1. 用户登录/注册成功后，服务器返回 JWT token
2. 客户端将 token 保存在本地存储和全局状态中
3. 后续请求自动带上 token
4. token 过期或无效时，自动跳转登录页面

## 错误处理

- 网络错误：显示网络异常提示
- 401 未授权：清除 token 并跳转到登录页
- 其他错误：显示服务器返回的错误信息

## 数据缓存策略

- 用户信息：登录后缓存到全局状态
- 热门话题：缓存到全局状态
- 用户和话题详情：按需缓存到全局状态的缓存对象中

## 最佳实践

1. 所有 API 请求使用 api.js 中封装的方法
2. 表单提交前进行前端验证
3. 显示加载状态和禁用按钮防止重复提交
4. 使用全局状态管理共享数据
5. API 请求失败时提供友好的错误提示 