/* 仪表板样式 */
.dashboard {
  padding: 0;
}

.dashboard .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.dashboard .ant-card-head-title {
  font-weight: 600;
  color: #333;
}

/* 实时数据卡片 */
.realtime-card {
  background: linear-gradient(135deg, #2b85e4 0%, #6ba7f0 100%);
  color: white;
}

.realtime-card .ant-card-head-title {
  color: white;
}

.realtime-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.realtime-header h3 {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.realtime-item {
  text-align: center;
  padding: 8px;
}

.realtime-value {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.realtime-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 统计卡片样式 */
.dashboard .ant-statistic {
  text-align: center;
}

.dashboard .ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.dashboard .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.dashboard .ant-statistic-content-prefix {
  margin-right: 8px;
  font-size: 20px;
}

.stat-detail {
  text-align: center;
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

/* 趋势指示器 */
.trend-up {
  color: #52c41a;
  font-size: 12px;
  margin-left: 8px;
}

.trend-down {
  color: #ff4d4f;
  font-size: 12px;
  margin-left: 8px;
}

.trend-neutral {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.engagement-score {
  color: #faad14;
  font-size: 12px;
  margin-left: 8px;
}

/* 图表容器 */
.dashboard .recharts-wrapper {
  margin: 16px 0;
}

/* 热门内容列表 */
.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2b85e4 0%, #6ba7f0 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.post-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.post-title {
  flex: 1;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 600;
  color: #333;
}

.dashboard .ant-list-item-meta-description {
  color: #666;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard .ant-statistic-content {
    font-size: 20px;
  }
  
  .dashboard .ant-statistic-content-prefix {
    font-size: 16px;
  }
}
