// 用户相关类型
export interface User {
  id: string
  username: string
  email?: string
  phone?: string
  avatar?: string
  role: 'admin' | 'teacher' | 'student'
  gender?: 'male' | 'female'
  bio?: string
  school?: string
  department?: string
  isDisabled: boolean
  createdAt: string
  updatedAt: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
}

// 登录响应
export interface LoginResponse {
  user: User
  token: string
}

// 帖子相关类型
export interface Post {
  id: string
  title: string
  content: string
  images?: string[]
  authorId: string
  categoryId: number
  status: 'published' | 'draft' | 'deleted'
  viewCount: number
  likeCount: number
  commentCount: number
  favoriteCount: number
  isTop: boolean
  createdAt: string
  updatedAt: string
  author?: User
  category?: Category
}

// 评论相关类型
export interface Comment {
  id: string
  content: string
  authorId: string
  postId: string
  replyTo?: string
  likeCount: number
  status: 'normal' | 'deleted'
  createdAt: string
  updatedAt: string
  author?: User
  post?: Post
  replyToComment?: Comment
}

// 分类相关类型
export interface Category {
  id: number
  name: string
  description?: string
  icon?: string
  sort: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 话题相关类型
export interface Topic {
  id: number
  name: string
  description?: string
  postCount: number
  isHot: boolean
  createdAt: string
  updatedAt: string
}

// 统计数据类型
export interface Statistics {
  userCount: number
  postCount: number
  commentCount: number
  todayActiveUsers: number
  todayNewUsers: number
  todayNewPosts: number
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 表格列类型
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
}

// 菜单项类型
export interface MenuItem {
  path: string
  name: string
  title: string
  icon?: string
  children?: MenuItem[]
}
