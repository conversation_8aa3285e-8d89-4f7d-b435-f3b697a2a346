{"name": "zip-stream", "version": "4.1.1", "description": "a streaming zip archive generator.", "homepage": "https://github.com/archiverjs/node-zip-stream", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-zip-stream.git"}, "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js"], "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "dependencies": {"archiver-utils": "^3.0.4", "compress-commons": "^4.1.2", "readable-stream": "^3.6.0"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.3.8", "jsdoc": "3.6.11", "minami": "1.2.3", "mkdirp": "2.1.6", "mocha": "9.2.2", "rimraf": "3.0.2"}, "keywords": ["archive", "stream", "zip-stream", "zip"]}