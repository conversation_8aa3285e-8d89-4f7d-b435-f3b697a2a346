<template>
  <view class="follow-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @tap="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">{{ pageTitle }}</view>
      <view class="nav-right"></view>
    </view>
    
    <!-- 标签页 -->
    <view class="tabs-container">
      <view class="tabs">
        <view 
          :class="['tab-item', { active: currentTab === 'following' }]"
          @tap="switchTab('following')"
        >
          <text class="tab-text">关注 {{ followingCount }}</text>
          <view class="tab-indicator" v-if="currentTab === 'following'"></view>
        </view>
        <view 
          :class="['tab-item', { active: currentTab === 'followers' }]"
          @tap="switchTab('followers')"
        >
          <text class="tab-text">粉丝 {{ followersCount }}</text>
          <view class="tab-indicator" v-if="currentTab === 'followers'"></view>
        </view>
      </view>
    </view>
    
    <!-- 内容区 -->
    <swiper 
      class="content-swiper" 
      :current="tabIndex" 
      @change="handleSwiperChange"
      :duration="300"
    >
      <!-- 关注列表 -->
      <swiper-item class="swiper-item">
        <scroll-view 
          scroll-y 
          class="scroll-view" 
          @scrolltolower="loadMore('following')"
          refresher-enabled 
          :refresher-triggered="followingRefreshing" 
          @refresherrefresh="refreshFollowing"
        >
          <view class="user-list" v-if="followingList.length > 0">
            <view 
              v-for="(user, index) in followingList" 
              :key="user.id"
              class="user-item"
              @tap="goToUserProfile(user.id)"
            >
              <view class="user-avatar">
                <image :src="user.avatar || '/static/images/common/default-avatar.png'" mode="aspectFill"></image>
              </view>
              <view class="user-info">
                <view class="user-nickname">{{ user.nickname || user.username }}</view>
                <view class="user-bio" v-if="user.bio">{{ user.bio }}</view>
                <view class="user-stats">
                  <text class="stat-item">{{ user.postCount || 0 }} 帖子</text>
                  <text class="stat-item">{{ user.followersCount || 0 }} 粉丝</text>
                </view>
              </view>
              <view class="user-action">
                <view 
                  :class="['follow-btn', { following: user.isFollowing }]"
                  @tap.stop="toggleFollow(user)"
                >
                  {{ user.isFollowing ? '已关注' : '关注' }}
                </view>
              </view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-container" v-else>
            <image class="empty-image" src="/static/images/common/empty-follow.png" mode="aspectFit"></image>
            <text class="empty-text">{{ followingLoading ? '加载中...' : '暂无关注用户' }}</text>
          </view>
          
          <!-- 加载更多 -->
          <view class="load-more" v-if="followingHasMore && followingList.length > 0">
            <text class="load-more-text">正在加载更多...</text>
          </view>
        </scroll-view>
      </swiper-item>
      
      <!-- 粉丝列表 -->
      <swiper-item class="swiper-item">
        <scroll-view 
          scroll-y 
          class="scroll-view" 
          @scrolltolower="loadMore('followers')"
          refresher-enabled 
          :refresher-triggered="followersRefreshing" 
          @refresherrefresh="refreshFollowers"
        >
          <view class="user-list" v-if="followersList.length > 0">
            <view 
              v-for="(user, index) in followersList" 
              :key="user.id"
              class="user-item"
              @tap="goToUserProfile(user.id)"
            >
              <view class="user-avatar">
                <image :src="user.avatar || '/static/images/common/default-avatar.png'" mode="aspectFill"></image>
              </view>
              <view class="user-info">
                <view class="user-nickname">{{ user.nickname || user.username }}</view>
                <view class="user-bio" v-if="user.bio">{{ user.bio }}</view>
                <view class="user-stats">
                  <text class="stat-item">{{ user.postCount || 0 }} 帖子</text>
                  <text class="stat-item">{{ user.followersCount || 0 }} 粉丝</text>
                </view>
              </view>
              <view class="user-action">
                <view 
                  :class="['follow-btn', { following: user.isFollowing }]"
                  @tap.stop="toggleFollow(user)"
                >
                  {{ user.isFollowing ? '已关注' : '关注' }}
                </view>
              </view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-container" v-else>
            <image class="empty-image" src="/static/images/common/empty-follow.png" mode="aspectFit"></image>
            <text class="empty-text">{{ followersLoading ? '加载中...' : '暂无粉丝' }}</text>
          </view>
          
          <!-- 加载更多 -->
          <view class="load-more" v-if="followersHasMore && followersList.length > 0">
            <text class="load-more-text">正在加载更多...</text>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import api from '@/api';

export default {
  data() {
    return {
      userId: '',
      currentTab: 'following',
      tabIndex: 0,
      
      // 关注列表
      followingList: [],
      followingPage: 1,
      followingPageSize: 20,
      followingHasMore: true,
      followingRefreshing: false,
      followingLoading: false,
      followingCount: 0,
      
      // 粉丝列表
      followersList: [],
      followersPage: 1,
      followersPageSize: 20,
      followersHasMore: true,
      followersRefreshing: false,
      followersLoading: false,
      followersCount: 0
    };
  },
  
  computed: {
    pageTitle() {
      return this.currentTab === 'following' ? '关注列表' : '粉丝列表';
    }
  },
  
  onLoad(options) {
    this.userId = options.userId || '';
    this.currentTab = options.type || 'following';
    this.tabIndex = this.currentTab === 'following' ? 0 : 1;
    
    this.loadData();
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 切换标签页
    switchTab(tab) {
      this.currentTab = tab;
      this.tabIndex = tab === 'following' ? 0 : 1;
    },
    
    // 滑动切换
    handleSwiperChange(e) {
      const index = e.detail.current;
      this.tabIndex = index;
      this.currentTab = index === 0 ? 'following' : 'followers';
    },
    
    // 加载数据
    loadData() {
      if (this.currentTab === 'following') {
        this.loadFollowing();
      } else {
        this.loadFollowers();
      }
    },
    
    // 加载关注列表
    loadFollowing() {
      if (this.followingLoading) return;
      
      this.followingLoading = true;
      
      // 模拟API调用
      setTimeout(() => {
        const mockData = [
          {
            id: '1',
            nickname: '用户1',
            avatar: '',
            bio: '这是一个测试用户',
            postCount: 10,
            followersCount: 100,
            isFollowing: true
          }
        ];
        
        if (this.followingPage === 1) {
          this.followingList = mockData;
        } else {
          this.followingList = [...this.followingList, ...mockData];
        }
        
        this.followingCount = this.followingList.length;
        this.followingHasMore = false;
        this.followingLoading = false;
        this.followingRefreshing = false;
      }, 1000);
    },
    
    // 加载粉丝列表
    loadFollowers() {
      if (this.followersLoading) return;
      
      this.followersLoading = true;
      
      // 模拟API调用
      setTimeout(() => {
        const mockData = [
          {
            id: '2',
            nickname: '粉丝1',
            avatar: '',
            bio: '这是一个粉丝用户',
            postCount: 5,
            followersCount: 50,
            isFollowing: false
          }
        ];
        
        if (this.followersPage === 1) {
          this.followersList = mockData;
        } else {
          this.followersList = [...this.followersList, ...mockData];
        }
        
        this.followersCount = this.followersList.length;
        this.followersHasMore = false;
        this.followersLoading = false;
        this.followersRefreshing = false;
      }, 1000);
    },
    
    // 刷新关注列表
    refreshFollowing() {
      this.followingRefreshing = true;
      this.followingPage = 1;
      this.loadFollowing();
    },
    
    // 刷新粉丝列表
    refreshFollowers() {
      this.followersRefreshing = true;
      this.followersPage = 1;
      this.loadFollowers();
    },
    
    // 加载更多
    loadMore(type) {
      if (type === 'following' && this.followingHasMore) {
        this.followingPage++;
        this.loadFollowing();
      } else if (type === 'followers' && this.followersHasMore) {
        this.followersPage++;
        this.loadFollowers();
      }
    },
    
    // 切换关注状态
    toggleFollow(user) {
      user.isFollowing = !user.isFollowing;
      
      uni.showToast({
        title: user.isFollowing ? '关注成功' : '取消关注',
        icon: 'success'
      });
    },
    
    // 跳转到用户主页
    goToUserProfile(userId) {
      uni.navigateTo({
        url: `/pages/profile/profile?userId=${userId}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
@import '@/styles/mixins.scss';

.follow-page {
  height: 100vh;
  background-color: $bg-color;
}

/* 导航栏 */
.nav-bar {
  @include flex(row, space-between, center);
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid $border-color;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 80rpx;
  height: 60rpx;
  @include center;
}

.nav-left {
  .iconfont {
    font-size: 36rpx;
    color: $text-primary;
  }
}

.nav-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-primary;
}

/* 标签页 */
.tabs-container {
  margin-top: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid $border-color;
}

.tabs {
  @include flex(row, center, center);
}

.tab-item {
  @include flex(column, center, center);
  position: relative;
  padding: 24rpx 0;
  flex: 1;
  transition: all 0.3s;
  
  &.active {
    color: $primary-color;
    font-weight: 500;
  }
}

.tab-text {
  font-size: $font-size-md;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: $primary-color;
  border-radius: $radius-sm;
}

/* 内容区 */
.content-swiper {
  height: calc(100vh - 176rpx);
}

.swiper-item {
  height: 100%;
}

.scroll-view {
  height: 100%;
}

/* 用户列表 */
.user-list {
  padding: 20rpx;
}

.user-item {
  @include flex(row, space-between, center);
  background-color: #fff;
  border-radius: $radius-lg;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: $radius-circle;
  overflow: hidden;
  margin-right: 24rpx;
  
  image {
    width: 100%;
    height: 100%;
  }
}

.user-info {
  flex: 1;
}

.user-nickname {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: 8rpx;
}

.user-bio {
  font-size: $font-size-sm;
  color: $text-secondary;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.user-stats {
  @include flex(row, flex-start, center);
}

.stat-item {
  font-size: $font-size-xs;
  color: $text-tertiary;
  margin-right: 24rpx;
}

.user-action {
  margin-left: 20rpx;
}

.follow-btn {
  @include center;
  min-width: 120rpx;
  height: 60rpx;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  font-weight: 500;
  transition: all 0.3s;
  
  &:not(.following) {
    background-color: $primary-color;
    color: #fff;
    
    &:active {
      background-color: darken($primary-color, 10%);
    }
  }
  
  &.following {
    background-color: $bg-color;
    color: $text-secondary;
    border: 1rpx solid $border-color;
    
    &:active {
      background-color: darken($bg-color, 5%);
    }
  }
}

/* 空状态 */
.empty-container {
  @include flex(column, center, center);
  padding: 120rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: $font-size-md;
  color: $text-tertiary;
}

/* 加载更多 */
.load-more {
  @include center;
  padding: 40rpx 0;
}

.load-more-text {
  font-size: $font-size-sm;
  color: $text-tertiary;
}
</style>
