# 校园墙后台管理系统

一个基于 React + TypeScript + Ant Design 构建的现代化后台管理系统，用于管理校园墙社交平台。

## 🚀 功能特性

### 核心功能
- **仪表板** - 实时数据统计、图表分析、热门内容展示
- **用户管理** - 用户列表、编辑、禁用/启用、角色管理
- **帖子管理** - 帖子审核、删除、置顶、状态管理
- **评论管理** - 评论审核、删除、回复管理
- **系统设置** - 分类管理、话题管理、系统配置

### 技术特性
- **现代化UI** - 基于 Ant Design 5.x 的企业级设计
- **响应式设计** - 支持桌面端和移动端
- **TypeScript** - 完整的类型安全
- **状态管理** - 使用 Zustand 轻量级状态管理
- **错误处理** - 完善的错误边界和异常处理
- **权限控制** - 基于角色的访问控制

## 📦 技术栈

- **前端框架**: React 18 + TypeScript
- **UI组件库**: Ant Design 5.x
- **状态管理**: Zustand
- **路由管理**: React Router v6
- **HTTP客户端**: Axios
- **图表库**: Recharts
- **构建工具**: Vite
- **样式**: CSS + CSS Modules

## 🛠 开发环境

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd admin
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3001

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🔐 登录信息

### 默认管理员账号
- **用户名**: admin
- **密码**: admin123

> 注意：这是演示账号，生产环境请修改默认密码

## 📁 项目结构

```
admin/
├── public/                 # 静态资源
│   ├── logo.svg           # 应用图标
│   └── vite.svg           # Vite图标
├── src/
│   ├── components/         # 通用组件
│   │   ├── Layout/        # 布局组件
│   │   ├── ErrorBoundary/ # 错误边界
│   │   └── Loading/       # 加载组件
│   ├── pages/             # 页面组件
│   │   ├── Auth/          # 认证页面
│   │   ├── Dashboard/     # 仪表板
│   │   ├── Users/         # 用户管理
│   │   ├── Posts/         # 帖子管理
│   │   ├── Comments/      # 评论管理
│   │   └── Settings/      # 系统设置
│   ├── services/          # API服务
│   │   ├── auth.ts        # 认证服务
│   │   └── dashboard.ts   # 仪表板服务
│   ├── stores/            # 状态管理
│   │   └── auth.ts        # 认证状态
│   ├── utils/             # 工具函数
│   │   ├── request.ts     # HTTP请求封装
│   │   └── index.ts       # 通用工具
│   ├── types/             # TypeScript类型定义
│   │   └── index.ts       # 类型定义
│   ├── App.tsx            # 应用入口
│   ├── main.tsx           # 主入口文件
│   └── index.css          # 全局样式
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript配置
├── vite.config.ts         # Vite配置
└── README.md              # 项目说明
```

## 🔧 配置说明

### API代理配置
在 `vite.config.ts` 中配置了API代理：
```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:3000',
      changeOrigin: true,
    },
  },
}
```

### 环境变量
可以在 `.env` 文件中配置环境变量：
```
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_TITLE=校园墙管理系统
```

## 🎨 主题定制

在 `src/main.tsx` 中可以自定义主题：
```typescript
<ConfigProvider 
  theme={{
    token: {
      colorPrimary: '#2b85e4',
      borderRadius: 8,
    },
  }}
>
```

## 📊 功能模块详解

### 仪表板
- 实时数据展示（在线用户、今日访问等）
- 统计卡片（用户、帖子、评论数据）
- 趋势图表（活跃度、内容发布趋势）
- 热门内容排行榜

### 用户管理
- 用户列表分页展示
- 用户信息编辑
- 用户状态管理（启用/禁用）
- 角色权限控制

### 帖子管理
- 帖子列表和搜索
- 帖子详情查看
- 帖子审核和删除
- 帖子置顶管理

### 评论管理
- 评论列表和筛选
- 评论审核和删除
- 回复关系展示
- 敏感内容标记

### 系统设置
- 分类管理（增删改查）
- 话题管理（热门话题）
- 系统配置（功能开关、限制设置）

## 🔒 安全特性

- JWT Token认证
- 路由权限守卫
- API请求拦截
- XSS防护
- 错误边界处理

## 🚀 部署说明

### 构建生产版本
```bash
npm run build
```

### 部署到服务器
将 `dist` 目录上传到服务器，配置 Nginx：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📝 开发指南

### 添加新页面
1. 在 `src/pages/` 下创建新目录
2. 创建 `index.tsx` 和 `index.css`
3. 在 `App.tsx` 中添加路由
4. 在 `Layout` 组件中添加菜单项

### 添加新API
1. 在 `src/services/` 下创建服务文件
2. 使用 `request` 工具发送请求
3. 定义相关的 TypeScript 类型

### 状态管理
使用 Zustand 创建新的状态存储：
```typescript
import { create } from 'zustand'

interface Store {
  data: any[]
  setData: (data: any[]) => void
}

export const useStore = create<Store>((set) => ({
  data: [],
  setData: (data) => set({ data }),
}))
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

© 2024 校园墙管理系统. All rights reserved.
