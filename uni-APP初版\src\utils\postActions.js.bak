// 帖子交互行为统一管理
import api from './api.js';
import store from './store.js';

// 点赞和收藏状态缓存过期时间（毫秒）
const CACHE_EXPIRATION = 12 * 60 * 60 * 1000; // 12小时
// 最大重试次数
const MAX_RETRIES = 1;
// 请求超时时间
const REQUEST_TIMEOUT = 8000; // 8秒
// 状态同步间隔时间
const SYNC_INTERVAL = 1000; // 1秒
// 状态更新额外延迟
const UPDATE_DELAY = 100; // 100毫秒
// 活动页面监听常量
const ACTIVE_PAGES = {
  INDEX: 'index',
  DETAIL: 'detail',
  PROFILE: 'profile',
  TOPIC: 'topic'
};

// 全局状态映射 - 用于跨页面状态同步
const globalPostStateMap = new Map();
// 收藏状态映射 - 专门用于收藏状态同步
const collectStateMap = new Map();
// 最后状态更新时间戳映射
const lastUpdateTimeMap = new Map();
// 当前活动页面
let activePage = null;
// 状态初始化标记 - 用于防止重复初始化
let initializedPosts = new Set();
// 最后同步时间
let lastSyncTime = 0;
// 状态同步定时器
let syncTimer = null;
// 全局状态版本号 - 用于检测状态是否有更新
let globalStateVersion = 0;

/**
 * 设置当前活动页面
 * @param {String} pageName 页面名称
 * @param {Object} pageContext 页面上下文或附加数据
 */
function setActivePage(pageName, pageContext = {}) {
  console.log('设置当前活动页面:', pageName, pageContext);
  
  // 记录上一个页面
  const previousPage = activePage ? { ...activePage } : null;
  
  // 更新当前页面
  activePage = { name: pageName, context: pageContext, time: Date.now() };
  
  // 记录到sessionStorage以便页面刷新时恢复
  try {
    uni.setStorageSync('active_page', JSON.stringify(activePage));
  } catch (e) {
    console.error('保存活动页面信息失败:', e);
  }
  
  // 当页面变化时进行额外的操作
  if (previousPage && previousPage.name !== pageName) {
    console.log(`页面从 ${previousPage.name} 切换到 ${pageName}`);
    
    // 如果从首页切换到详情页，同步当前帖子的状态
    if (previousPage.name === ACTIVE_PAGES.INDEX && pageName === ACTIVE_PAGES.DETAIL && pageContext?.postId) {
      const postId = pageContext.postId;
      console.log(`从首页进入帖子详情页 ${postId}，同步状态`);
      
      // 如果全局状态映射中有该帖子的状态，同步到store
      if (globalPostStateMap.has(postId)) {
        const cachedState = globalPostStateMap.get(postId);
        console.log(`发现全局缓存的帖子 ${postId} 状态:`, cachedState);
        
        // 更新全局store
        store.mutations.updatePostStatus(postId, cachedState);
        
        // 确保收藏状态被正确记录
        if (cachedState.isCollected !== undefined) {
          collectStateMap.set(postId, cachedState.isCollected);
          console.log(`更新收藏状态映射: ${postId} -> ${cachedState.isCollected}`);
        }
      } 
      
      // 页面切换时强制同步状态
      setTimeout(() => {
        forceRefreshPostStatus(postId);
      }, UPDATE_DELAY);
    }
    
    // 如果从详情页返回首页，同步所有可见帖子的状态
    if (previousPage.name === ACTIVE_PAGES.DETAIL && pageName === ACTIVE_PAGES.INDEX && previousPage.context?.postId) {
      const postId = previousPage.context.postId;
      console.log(`从帖子详情页 ${postId} 返回首页，同步状态`);
      
      // 如果全局状态映射中有该帖子的状态，同步给所有页面
      if (globalPostStateMap.has(postId)) {
        const cachedState = globalPostStateMap.get(postId);
        
        // 确保收藏状态被正确记录
        if (cachedState.isCollected !== undefined) {
          collectStateMap.set(postId, cachedState.isCollected);
          console.log(`从详情页返回时更新收藏状态映射: ${postId} -> ${cachedState.isCollected}`);
        }
      }
      
      // 延迟一点时间再进行全局同步，确保页面已加载完成
      setTimeout(() => {
        broadcastStateChanges();
      }, UPDATE_DELAY);
    }
  }
  
  // 当页面变化时，开始状态同步
  startStatusSync();
}

/**
 * 获取当前活动页面
 * @returns {Object|null} 活动页面信息
 */
function getActivePage() {
  if (activePage) return activePage;
  
  // 尝试从sessionStorage恢复
  try {
    const storedPage = uni.getStorageSync('active_page');
    if (storedPage) {
      activePage = JSON.parse(storedPage);
      return activePage;
    }
  } catch (e) {
    console.error('获取活动页面信息失败:', e);
  }
  
  return null;
}

/**
 * 初始化帖子状态 - 主动从服务器同步
 * @param {Array|Object} posts 帖子数组或单个帖子对象
 * @param {Boolean} forceRefresh 是否强制刷新
 * @returns {Promise<Object>} 状态同步结果
 */
async function initializePostStatus(posts, forceRefresh = false) {
  console.log('初始化帖子状态:', Array.isArray(posts) ? `${posts.length}个帖子` : '单个帖子', forceRefresh ? '(强制刷新)' : '');
  
  // 确保全局状态已恢复
  if (globalPostStateMap.size === 0) {
    restoreGlobalState();
  }
  
  // 将单个帖子转换为数组处理
  const postArray = Array.isArray(posts) ? posts : [posts];
  if (postArray.length === 0) return { success: true, total: 0, synced: 0 };
  
  // 收集帖子ID
  let postIds = postArray.map(post => post.id);
  
  // 先从全局映射中获取已缓存的状态
  let updatedFromGlobal = 0;
  postIds.forEach(id => {
    // 检查是否有全局缓存状态
    if (globalPostStateMap.has(id)) {
      const globalState = globalPostStateMap.get(id);
      const post = postArray.find(p => p.id === id);
      
      if (post && globalState) {
        // 应用全局缓存中的状态到帖子对象
        console.log(`从全局映射更新帖子 ${id} 状态`);
        
        // 保存原始状态用于日志
        const originalLiked = post.isLiked;
        const originalCollected = post.isCollected;
        
        // 更新帖子对象
        if (globalState.isLiked !== undefined) post.isLiked = globalState.isLiked;
        if (globalState.isCollected !== undefined) post.isCollected = globalState.isCollected;
        if (globalState.likes !== undefined) post.likes = globalState.likes;
        if (globalState.collections !== undefined) post.collections = globalState.collections;
        
        // 记录状态变化
        if (originalLiked !== post.isLiked) {
          console.log(`帖子 ${id} 点赞状态从 ${originalLiked} 更新为 ${post.isLiked} (全局缓存)`);
        }
        if (originalCollected !== post.isCollected) {
          console.log(`帖子 ${id} 收藏状态从 ${originalCollected} 更新为 ${post.isCollected} (全局缓存)`);
          
          // 确保收藏状态同步到专用映射
          collectStateMap.set(id, post.isCollected);
        }
        
        updatedFromGlobal++;
        
        // 标记已初始化
        initializedPosts.add(id);
      }
    }
  });
  
  if (updatedFromGlobal > 0) {
    console.log(`从全局状态映射更新了 ${updatedFromGlobal} 个帖子状态`);
  }
  
  // 过滤已初始化的帖子（除非强制刷新）
  if (!forceRefresh) {
    postIds = postIds.filter(id => !initializedPosts.has(id));
    if (postIds.length === 0) {
      console.log('所有帖子都已初始化，跳过API请求');
      return { 
        success: true, 
        total: postArray.length, 
        synced: 0, 
        skipped: postArray.length,
        updatedFromGlobal
      };
    }
  }
  
  console.log('需要从API初始化的帖子:', postIds.join(', '));
  
  // 先从本地存储加载缓存状态
  const loadResult = loadPostStatusesFromStorage(postIds);
  console.log(`从本地存储加载了 ${loadResult.loaded} 个帖子状态`);
  
  // 强制刷新、存在未初始化的帖子或来自详情页返回时，才向服务器请求最新状态
  if (forceRefresh || postIds.length > 0 || getActivePage()?.name === ACTIVE_PAGES.INDEX) {
    try {
      // 向服务器请求批量状态
      const result = await refreshPostStatuses(postIds);
      
      if (result && result.success && result.synced > 0) {
        // 更新帖子对象
        postArray.forEach(post => {
          if (post && post.id && globalPostStateMap.has(post.id)) {
            const globalState = globalPostStateMap.get(post.id);
            
            // 应用全局缓存中的状态到帖子对象
            if (globalState.isLiked !== undefined) post.isLiked = globalState.isLiked;
            if (globalState.isCollected !== undefined) post.isCollected = globalState.isCollected;
            if (globalState.likes !== undefined) post.likes = globalState.likes;
            if (globalState.collections !== undefined) post.collections = globalState.collections;
            
            // 标记已初始化
            initializedPosts.add(post.id);
          }
        });
        
        // 广播状态变化到所有页面
        broadcastStateChanges();
      }
      
      // 标记已初始化
      postIds.forEach(id => initializedPosts.add(id));
      
      return {
        ...result,
        updatedFromGlobal
      };
    } catch (error) {
      console.error('初始化帖子状态失败:', error);
      return { 
        success: false, 
        error, 
        total: postIds.length, 
        synced: 0,
        updatedFromGlobal
      };
    }
  }
  
  return { 
    success: true, 
    total: postArray.length, 
    synced: 0, 
    fromCache: true,
    updatedFromGlobal
  };
}

/**
 * 加载本地存储的帖子状态到内存
 * @param {Array} postIds 帖子ID数组
 * @returns {Object} 加载结果
 */
function loadPostStatusesFromStorage(postIds) {
  if (!postIds || !Array.isArray(postIds) || postIds.length === 0) {
    return { loaded: 0, expired: 0, total: 0 };
  }
  
  console.log('从本地存储加载帖子状态:', postIds.join(', '));
  
  let loaded = 0;
  let expired = 0;
  const now = Date.now();
  
  try {
    postIds.forEach(id => {
      if (!id) return;
      
      // 如果全局映射中已存在且最近更新过，优先使用全局映射
      if (globalPostStateMap.has(id) && lastUpdateTimeMap.has(id)) {
        const lastUpdate = lastUpdateTimeMap.get(id);
        if (now - lastUpdate < CACHE_EXPIRATION) {
          // 全局映射中的状态足够新，无需从localStorage重新加载
          const globalState = globalPostStateMap.get(id);
          
          // 更新到store (确保数据模型一致性)
          store.mutations.updatePostStatus(id, {
            _timestamp: lastUpdate,
            isLiked: globalState.isLiked,
            isCollected: globalState.isCollected,
            likes: globalState.likes,
            collections: globalState.collections
          });
          
          // 标记为已初始化
          initializedPosts.add(id);
          loaded++;
          return;
        }
      }
      
      // 否则尝试从localStorage加载
      const cacheKey = `post_status_${id}`;
      try {
        const statusData = uni.getStorageSync(cacheKey);
        if (statusData) {
          const status = JSON.parse(statusData);
          
          // 检查缓存是否过期
          if (status._timestamp && now - status._timestamp < CACHE_EXPIRATION) {
            // 更新到store
            store.mutations.updatePostStatus(id, status);
            
            // 同时更新到全局映射
            globalPostStateMap.set(id, {
              isLiked: status.isLiked,
              isCollected: status.isCollected,
              likes: status.likes,
              collections: status.collections
            });
            
            // 更新专用收藏状态映射
            collectStateMap.set(id, status.isCollected);
            
            // 记录更新时间
            lastUpdateTimeMap.set(id, status._timestamp);
            
            loaded++;
            
            // 标记为已初始化
            initializedPosts.add(id);
          } else {
            // 清除过期缓存
            uni.removeStorageSync(cacheKey);
            expired++;
          }
        }
      } catch (e) {
        console.error(`加载帖子${id}状态缓存失败:`, e);
      }
    });
    
    console.log(`从本地加载帖子状态结果: 成功=${loaded}, 过期=${expired}, 总数=${postIds.length}`);
    
    // 如果有加载到数据，尝试广播状态更新
    if (loaded > 0) {
      setTimeout(() => {
        broadcastStateChanges();
      }, UPDATE_DELAY);
    }
    
    return { loaded, expired, total: postIds.length };
  } catch (error) {
    console.error('批量加载帖子状态缓存失败:', error);
    return { loaded: 0, expired: 0, total: postIds.length, error };
  }
}

/**
 * 批量刷新帖子状态
 * @param {Array} postIds 帖子ID数组
 * @returns {Promise<Object>} 刷新结果
 */
async function refreshPostStatuses(postIds) {
  if (!postIds || postIds.length === 0) return { success: true, total: 0, synced: 0 };
  
  // 过滤无效的帖子ID
  const validPostIds = postIds.filter(id => id !== undefined && id !== null);
  
  if (validPostIds.length === 0) {
    console.log('没有有效的帖子ID需要刷新');
    return { success: true, total: 0, synced: 0 };
  }
  
  console.log('批量刷新帖子状态:', validPostIds.join(', '));
  
  // 验证用户登录状态
  const isLoggedIn = verifyUserLoginStatus();
  if (!isLoggedIn) {
    console.warn('用户未登录，无法获取帖子状态');
    return { success: false, error: '用户未登录', total: validPostIds.length, synced: 0, notLoggedIn: true };
  }
  
  const token = uni.getStorageSync('token');
  const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
  
  try {
    // 添加随机参数，避免缓存
    const randomParam = Math.random().toString(36).substring(2, 15);
    
    // 使用API批量检查状态 - 由于没有批量API，使用循环单独查询
    // 存储所有帖子状态
    const statuses = {};
    
    // 限制并发请求
    const batchSize = 3; // 一次最多处理3个请求
    
    for (let i = 0; i < validPostIds.length; i += batchSize) {
      const batch = validPostIds.slice(i, i + batchSize);
      console.log(`处理帖子状态批次 ${Math.floor(i/batchSize) + 1}/${Math.ceil(validPostIds.length/batchSize)}:`, batch);
      
      // 并行处理一批请求
      const batchPromises = batch.map(async (postId) => {
        try {
          if (!postId) {
            console.error('无效的帖子ID:', postId);
            return;
          }
          
          // 使用批量API获取单个帖子状态
          const result = await api.batch.getPostStatus(postId);
          
          // 如果成功，保存状态数据
          if (result && result.success && result.data) {
            console.log(`获取帖子${postId}状态成功:`, result.data);
            
            // 直接使用服务器返回的状态
            const statusData = {
              isLiked: result.data.isLiked,
              isCollected: result.data.isCollected,
              likes: result.data.likes,
              collections: result.data.collections
            };
            
            statuses[postId] = statusData;
          } else {
            console.error(`获取帖子${postId}状态失败:`, result?.message || '未知错误');
          }
        } catch (err) {
          console.error(`获取帖子${postId}状态请求异常:`, err);
        }
      });
      
      // 等待当前批次完成
      await Promise.all(batchPromises);
      
      // 添加小延迟，防止请求过于频繁
      if (i + batchSize < validPostIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    const result = {
      success: true,
      data: statuses
    };
    
    if (result && result.success && result.data) {
      console.log('获取到批量帖子状态:', result.data);
      
      // 更新内存和存储
      const statuses = result.data;
      let synced = 0;
      const now = Date.now();
      
      Object.keys(statuses).forEach(postId => {
        const statusData = statuses[postId];
        
        if (statusData) {
          // 添加时间戳
          statusData._timestamp = now;
          
          // 更新内存状态
          store.mutations.updatePostStatus(postId, statusData);
          
          // 更新全局状态映射
          globalPostStateMap.set(postId, {
            isLiked: statusData.isLiked,
            isCollected: statusData.isCollected,
            likes: statusData.likes,
            collections: statusData.collections
          });
          
          // 更新专用收藏状态映射
          collectStateMap.set(postId, statusData.isCollected);
          
          // 更新最后更新时间
          lastUpdateTimeMap.set(postId, now);
          
          // 更新本地存储
          try {
            uni.setStorageSync(`post_status_${postId}`, JSON.stringify(statusData));
            synced++;
          } catch (e) {
            console.error(`缓存帖子${postId}状态失败:`, e);
          }
        }
      });
      
      // 增加全局状态版本
      globalStateVersion++;
      
      console.log(`批量刷新帖子状态完成: 同步=${synced}/${validPostIds.length}`);
      return { success: true, total: validPostIds.length, synced };
    } else {
      console.error('批量获取帖子状态失败:', result ? result.message : '未知错误');
      return { success: false, error: result?.message || '获取状态失败', total: validPostIds.length, synced: 0 };
    }
  } catch (error) {
    console.error('批量刷新帖子状态出错:', error);
    return { success: false, error, total: validPostIds.length, synced: 0 };
  }
}

/**
 * 开始定时状态同步
 */
function startStatusSync() {
  if (syncTimer) {
    clearInterval(syncTimer);
  }
  
  lastSyncTime = Date.now();
  
  syncTimer = setInterval(() => {
    // 获取当前活动页面中的帖子ID，并同步状态
    const now = Date.now();
    if (now - lastSyncTime >= 10000) { // 至少10秒同步一次
      synchronizeActivePostsStatus();
      lastSyncTime = now;
    }
  }, SYNC_INTERVAL);
  
  console.log('已启动状态同步定时器');
}

/**
 * 停止定时状态同步
 */
function stopStatusSync() {
  if (syncTimer) {
    clearInterval(syncTimer);
    syncTimer = null;
    console.log('已停止状态同步定时器');
  }
}

/**
 * 同步当前活动页面的帖子状态
 * @returns {Promise<Object>} 同步结果
 */
async function synchronizeActivePostsStatus() {
  const page = getActivePage();
  if (!page) {
    console.log('没有活动页面，跳过同步');
    return { success: false, reason: '没有活动页面' };
  }
  
  console.log(`开始同步${page.name}页面帖子状态`);
  
  // 根据不同页面获取需要同步的帖子ID
  let postIds = [];
  
  switch (page.name) {
    case ACTIVE_PAGES.INDEX:
      // 首页 - 同步可见的帖子
      postIds = store.state.posts.map(post => post.id).filter(id => id);
      break;
      
    case ACTIVE_PAGES.DETAIL:
      // 详情页 - 同步当前查看的帖子
      if (page.context && page.context.postId) {
        postIds = [page.context.postId];
      } else if (store.state.currentPost && store.state.currentPost.id) {
        postIds = [store.state.currentPost.id];
      }
      
      // 检查帖子对象是否存在并且更新状态
      if (postIds.length > 0 && store.state.currentPost) {
        const postId = postIds[0];
        if (globalPostStateMap.has(postId)) {
          const globalState = globalPostStateMap.get(postId);
          const post = store.state.currentPost;
          
          // 更新帖子对象
          if (globalState.isLiked !== undefined) post.isLiked = globalState.isLiked;
          if (globalState.isCollected !== undefined) post.isCollected = globalState.isCollected;
          if (globalState.likes !== undefined) post.likes = globalState.likes;
          if (globalState.collections !== undefined) post.collections = globalState.collections;
          
          console.log(`已从全局状态更新详情页帖子 ${postId} 状态`);
        }
      }
      break;
      
    case ACTIVE_PAGES.PROFILE:
    case ACTIVE_PAGES.TOPIC:
      // 用户/话题页 - 同步当前页面的帖子
      postIds = store.state.posts.map(post => post.id).filter(id => id);
      break;
  }
  
  if (postIds.length > 0) {
    console.log(`同步当前${page.name}页面的${postIds.length}个帖子状态`, postIds);
    
    // 先检查是否有全局状态可用
    let updatedFromGlobal = 0;
    
    // 遍历当前页面上的帖子，应用全局状态
    if (page.name !== ACTIVE_PAGES.DETAIL && store.state.posts && store.state.posts.length > 0) {
      store.state.posts.forEach(post => {
        if (post && post.id && globalPostStateMap.has(post.id)) {
          const globalState = globalPostStateMap.get(post.id);
          
          // 保存原始状态
          const originalLiked = post.isLiked;
          const originalCollected = post.isCollected;
          
          // 应用全局状态
          if (globalState.isLiked !== undefined) post.isLiked = globalState.isLiked;
          if (globalState.isCollected !== undefined) post.isCollected = globalState.isCollected;
          if (globalState.likes !== undefined) post.likes = globalState.likes;
          if (globalState.collections !== undefined) post.collections = globalState.collections;
          
          // 记录状态变化
          if (originalLiked !== post.isLiked || originalCollected !== post.isCollected) {
            updatedFromGlobal++;
          }
        }
      });
      
      if (updatedFromGlobal > 0) {
        console.log(`从全局状态映射更新了${updatedFromGlobal}个帖子状态`);
      }
    }
    
    // 是否需要向服务器请求
    const now = Date.now();
    const needsServerSync = postIds.some(id => {
      if (!lastUpdateTimeMap.has(id)) return true;
      return now - lastUpdateTimeMap.get(id) > SYNC_INTERVAL;
    });
    
    if (needsServerSync) {
      console.log('需要从服务器同步最新状态');
      try {
        const result = await refreshPostStatuses(postIds);
        return { ...result, updatedFromGlobal };
      } catch (error) {
        console.error('同步活动页面帖子状态失败:', error);
        return { success: false, error, updatedFromGlobal };
      }
    } else {
      console.log('全部帖子状态都在缓存有效期内，跳过服务器同步');
      return { success: true, fromCache: true, updatedFromGlobal };
    }
  } else {
    console.log(`当前${page.name}页面没有需要同步的帖子`);
    return { success: true, noPosts: true };
  }
}

/**
 * 验证用户是否已登录且令牌有效
 * @returns {boolean} 是否已登录且令牌有效
 */
function verifyUserLoginStatus() {
  try {
    console.log('==== 开始验证用户登录状态 ====');
    
    // 1. 检查是否登录
    const isLogin = store.getters.isLogin();
    console.log('isLogin检查结果:', isLogin, typeof isLogin);
    
    if (!isLogin) {
      console.log('用户未登录，无法执行操作');
      return false;
    }
    
    // 2. 获取用户信息
    const userObj = store.getters.getUser();
    console.log('getUser检查结果:', userObj);
    
    if (!userObj) {
      console.log('未获取到用户信息');
      return false;
    }
    
    // 用户对象可能在user属性中
    const user = userObj.user || userObj;
    console.log('实际用户对象:', user);
    
    if (!user || !user.id) {
      console.log('用户信息不完整，缺少ID');
      return false;
    }
    
    // 3. 检查令牌是否存在
    const token = uni.getStorageSync('token');
    console.log('token检查结果:', token ? '令牌存在' : '令牌不存在', typeof token);
    
    if (!token) {
      console.log('找不到授权令牌，需要重新登录');
      return false;
    }
    
    console.log('==== 用户登录状态验证通过 ====');
    return true;
  } catch (error) {
    console.error('验证用户登录状态出错:', error);
    return false;
  }
}

/**
 * 尝试刷新令牌
 * @returns {Promise<boolean>} 是否刷新成功
 */
async function tryRefreshToken() {
  try {
    console.log('尝试刷新令牌...');
    // 检查是否有刷新令牌
    const refreshToken = uni.getStorageSync('refreshToken');
    if (!refreshToken) {
      console.log('没有刷新令牌，无法刷新');
      return false;
    }
    
    // 调用刷新令牌API（假设api.auth.refresh存在）
    if (api.auth.refresh) {
      const result = await api.auth.refresh(refreshToken);
      if (result && result.success && result.data && result.data.token) {
        // 保存新令牌
        uni.setStorageSync('token', result.data.token);
        console.log('令牌刷新成功');
        return true;
      }
    }
    
    console.log('令牌刷新失败');
    return false;
  } catch (error) {
    console.error('刷新令牌过程出错:', error);
    return false;
  }
}

/**
 * 处理API错误并尝试自动恢复
 * @param {Error} error API错误
 * @param {Function} retryCallback 重试回调函数
 * @returns {Promise<boolean>} 是否成功处理
 */
async function handleApiError(error, retryCallback) {
  // 检查错误是否为授权问题
  const isAuthError = error.status === 401 || 
                     (error.message && error.message.includes('授权')) ||
                     (error.message && error.message.includes('登录'));
  
  // 如果是授权问题，尝试刷新令牌
  if (isAuthError) {
    console.log('检测到授权问题，尝试刷新令牌');
    const refreshed = await tryRefreshToken();
    
    if (refreshed && retryCallback) {
      console.log('令牌已刷新，重试操作');
      return await retryCallback();
    } else {
      console.log('令牌刷新失败，需要用户重新登录');
      // 提示用户重新登录
      uni.showModal({
        title: '登录已过期',
        content: '您的登录已过期，请重新登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return false;
    }
  }
  
  // 其他类型错误处理
  let errorMessage = '操作失败';
  
  // 解析错误信息
  if (error.data && error.data.message) {
    errorMessage = error.data.message;
  } else if (error.message) {
    errorMessage = error.message;
  }
  
  // 根据错误类型提供友好提示
  if (error.status === 400) {
    if (errorMessage.includes('已经点赞') || errorMessage.includes('未点赞')) {
      errorMessage = '点赞状态已更新，请刷新页面';
    } else if (errorMessage.includes('已经收藏') || errorMessage.includes('未收藏')) {
      errorMessage = '收藏状态已更新，请刷新页面';
    } else if (errorMessage.includes('权限不足')) {
      errorMessage = '您没有执行此操作的权限';
    } else {
      errorMessage = '请求参数错误，请稍后再试';
    }
  } else if (error.status === 403) {
    errorMessage = '没有权限执行此操作';
  } else if (error.status === 404) {
    errorMessage = '请求的资源不存在';
  } else if (error.status === 500) {
    errorMessage = '服务器内部错误，请稍后再试';
  } else if (!window.navigator.onLine) {
    errorMessage = '网络连接已断开，请检查网络';
  }
  
  // 显示错误提示
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  });
  
  return false;
}

/**
 * 处理帖子点赞/取消点赞
 * @param {Object} post 帖子对象
 * @returns {Promise<boolean>} 操作是否成功
 */
async function handlePostLike(post) {
  if (!post || !post.id) {
    console.error('无效的帖子数据');
    return false;
  }

  // 验证用户登录状态
  if (!verifyUserLoginStatus()) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return false;
  }

  try {
    // 记录原始状态，用于回滚
    const originalLiked = post.isLiked;
    const originalLikes = post.likes || 0;
    
    // 乐观更新UI状态
    post.isLiked = !originalLiked;
    post.likes = post.likes + (post.isLiked ? 1 : -1);
    
    // 保存到本地存储
    savePostLikeStatus(post.id, post.isLiked);
    
    // 更新全局状态
    store.mutations.updatePost(post.id, {
      isLiked: post.isLiked,
      likes: post.likes
    });
    
    console.log(`执行帖子${post.isLiked ? '点赞' : '取消点赞'}操作:`, post.id);
    
    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);
    
    // 准备额外的请求头
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${uni.getStorageSync('token')}`
    };
    
    let retryCount = 0;
    let success = false;
    
    // 实现重试逻辑
    while (retryCount <= MAX_RETRIES && !success) {
      try {
        // 调用API
        const result = await (post.isLiked 
          ? api.posts.like(post.id, { headers }) 
          : api.posts.unlike(post.id, { headers })
        );
        
        clearTimeout(timeoutId);
        
        // 检查结果
        if (result && result.success) {
          console.log(`帖子${post.isLiked ? '点赞' : '取消点赞'}成功:`, post.id);
          success = true;
          
          // 使用服务器返回的实际状态更新
          if (result.data && result.data.likes !== undefined) {
            post.likes = result.data.likes;
            
            // 更新全局状态
            store.mutations.updatePost(post.id, {
              likes: post.likes
            });
          }
          
          return true;
        } else {
          // API操作失败，但请求成功发送
          console.error(`帖子${post.isLiked ? '点赞' : '取消点赞'}失败:`, result?.message || '未知错误');
          
          // 如果错误消息表明状态不一致，调整本地状态
          if (result && result.message) {
            if (result.message.includes('已经点赞')) {
              // 服务器已经点赞过，强制设置本地状态为已点赞
              post.isLiked = true;
              post.likes = Math.max(originalLikes, post.likes); // 保持较高的点赞数
              
              // 更新全局状态映射
              globalPostStateMap.set(post.id, {
                ...(globalPostStateMap.get(post.id) || {}),
                isLiked: true,
                likes: post.likes
              });
              
              // 更新最后更新时间
              lastUpdateTimeMap.set(post.id, Date.now());
              
              // 保存到本地存储
              savePostLikeStatus(post.id, true);
              
              // 更新全局状态
              store.mutations.updatePost(post.id, {
                isLiked: true,
                likes: post.likes
              });
              
              uni.showToast({
                title: '已经点过赞了',
                icon: 'none'
              });
              
              // 刷新获取准确计数
              setTimeout(() => refreshPostStatus(post.id), 500);
              
              return true;
            } else if (result.message.includes('未点赞')) {
              // 服务器未点赞过，强制设置本地状态为未点赞
              post.isLiked = false;
              post.likes = Math.max(0, originalLikes);
              
              // 更新全局状态映射
              globalPostStateMap.set(post.id, {
                ...(globalPostStateMap.get(post.id) || {}),
                isLiked: false,
                likes: post.likes
              });
              
              // 更新最后更新时间
              lastUpdateTimeMap.set(post.id, Date.now());
              
              // 保存到本地存储
              savePostLikeStatus(post.id, false);
              
              // 更新全局状态
              store.mutations.updatePost(post.id, {
                isLiked: false,
                likes: post.likes
              });
              
              uni.showToast({
                title: '尚未点过赞',
                icon: 'none'
              });
              
              // 刷新获取准确计数
              setTimeout(() => refreshPostStatus(post.id), 500);
              
              return true;
            } else {
              // 其他错误，尝试刷新状态
              await refreshPostStatus(post.id);
              
              // 显示提示
              uni.showToast({
                title: '操作状态已更新',
                icon: 'none'
              });
              
              return false;
            }
          }
          
          throw new Error(result?.message || '操作失败');
        }
      } catch (error) {
        retryCount++;
        console.error(`点赞操作失败 (尝试 ${retryCount}/${MAX_RETRIES + 1}):`, error);
        
        clearTimeout(timeoutId);
        
        if (retryCount <= MAX_RETRIES) {
          console.log(`将在1秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {
          // 处理错误
          await handleApiError(error, () => handlePostLike(post));
          
          // 回滚状态
          rollbackLikeStatus(post, originalLiked, originalLikes);
          return false;
        }
      }
    }
    
    // 如果执行到这里，说明重试失败
    if (!success) {
      // 回滚状态
      rollbackLikeStatus(post, originalLiked, originalLikes);
      
      // 显示错误提示
      uni.showToast({
        title: '网络异常，请稍后再试',
        icon: 'none'
      });
      
      return false;
    }
    
    return success;
  } catch (error) {
    console.error('点赞操作异常:', error);
    
    // 发生异常时回滚状态（注意此时状态已经被改变，需要取反）
    const originalLiked = !post.isLiked;
    const originalLikes = post.likes + (originalLiked ? 1 : -1);
    rollbackLikeStatus(post, originalLiked, originalLikes);
    
    // 显示错误提示
    uni.showToast({
      title: '操作失败，请稍后再试',
      icon: 'none'
    });
    
    return false;
  }
}

/**
 * 处理帖子收藏/取消收藏
 * @param {Object} post 帖子对象
 * @returns {Promise<boolean>} 操作是否成功
 */
async function handlePostCollect(post) {
  if (!post || !post.id) {
    console.error('无效的帖子数据');
    return false;
  }
  
  // 验证用户登录状态
  if (!verifyUserLoginStatus()) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return false;
  }
  
  try {
    // 记录原始状态，用于回滚
    const originalCollected = post.isCollected;
    const originalCollections = post.collections || 0;
    
    // 乐观更新UI状态
    post.isCollected = !originalCollected;
    post.collections = post.collections + (post.isCollected ? 1 : -1);
    
    // 保存到本地存储
    savePostCollectStatus(post.id, post.isCollected);
    
    // 更新全局状态映射
    globalPostStateMap.set(post.id, {
      ...(globalPostStateMap.get(post.id) || {}),
      isCollected: post.isCollected,
      collections: post.collections
    });
    
    // 更新专用收藏状态映射
    collectStateMap.set(post.id, post.isCollected);
    
    // 记录更新时间
    lastUpdateTimeMap.set(post.id, Date.now());
    
    // 增加状态版本号
    globalStateVersion++;
    
    // 更新全局状态
    store.mutations.updatePost(post.id, {
      isCollected: post.isCollected,
      collections: post.collections
    });
    
    // 广播状态变化
    setTimeout(broadcastStateChanges, UPDATE_DELAY);
    
    console.log(`执行帖子${post.isCollected ? '收藏' : '取消收藏'}操作:`, post.id);
    
    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);
    
    // 准备额外的请求头
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${uni.getStorageSync('token')}`
    };
    
    let retryCount = 0;
    let success = false;
    
    // 实现重试逻辑
    while (retryCount <= MAX_RETRIES && !success) {
      try {
        // 调用API
        const result = await (post.isCollected 
          ? api.posts.collect(post.id, { headers }) 
          : api.posts.uncollect(post.id, { headers })
        );
        
        clearTimeout(timeoutId);
        
        // 检查结果
        if (result && result.success) {
          console.log(`帖子${post.isCollected ? '收藏' : '取消收藏'}成功:`, post.id);
          success = true;
          
          // 使用服务器返回的实际状态更新
          if (result.data && result.data.collections !== undefined) {
            post.collections = result.data.collections;
            
            // 更新全局状态映射
            globalPostStateMap.set(post.id, {
              ...(globalPostStateMap.get(post.id) || {}),
              collections: post.collections
            });
            
            // 记录更新时间
            lastUpdateTimeMap.set(post.id, Date.now());
            
            // 更新全局状态
            store.mutations.updatePost(post.id, {
              collections: post.collections
            });
            
            // 广播状态变化
            broadcastStateChanges();
          }
          
          // 强制刷新当前帖子状态，确保与服务器一致
          setTimeout(() => forceRefreshPostStatus(post.id), 1000);
          
          // 显示成功提示
          uni.showToast({
            title: post.isCollected ? '收藏成功' : '已取消收藏',
            icon: 'success'
          });
          
          return true;
        } else {
          // API操作失败，但请求成功发送
          console.error(`帖子${post.isCollected ? '收藏' : '取消收藏'}失败:`, result?.message || '未知错误');
          
          // 如果错误消息表明状态不一致，处理状态差异
          if (result && result.message) {
            if (result.message.includes('已经收藏')) {
              // 服务器已经收藏过，强制设置本地状态为已收藏
              post.isCollected = true;
              post.collections = Math.max(originalCollections, post.collections); // 保持较高的收藏数
              
              // 更新全局状态映射
              globalPostStateMap.set(post.id, {
                ...(globalPostStateMap.get(post.id) || {}),
                isCollected: true,
                collections: post.collections
              });
              
              // 更新专用收藏状态映射
              collectStateMap.set(post.id, true);
              
              // 更新最后更新时间
              lastUpdateTimeMap.set(post.id, Date.now());
              
              // 保存到本地存储
              savePostCollectStatus(post.id, true);
              
              // 更新全局状态
              store.mutations.updatePost(post.id, {
                isCollected: true,
                collections: post.collections
              });
              
              uni.showToast({
                title: '已经收藏过了',
                icon: 'none'
              });
              
              // 刷新获取准确计数
              setTimeout(() => refreshPostStatus(post.id), 500);
              
              return true;
            } else if (result.message.includes('未收藏')) {
              // 服务器未收藏过，强制设置本地状态为未收藏
              post.isCollected = false;
              post.collections = Math.max(0, originalCollections);
              
              // 更新全局状态映射
              globalPostStateMap.set(post.id, {
                ...(globalPostStateMap.get(post.id) || {}),
                isCollected: false,
                collections: post.collections
              });
              
              // 更新专用收藏状态映射
              collectStateMap.set(post.id, false);
              
              // 更新最后更新时间
              lastUpdateTimeMap.set(post.id, Date.now());
              
              // 保存到本地存储
              savePostCollectStatus(post.id, false);
              
              // 更新全局状态
              store.mutations.updatePost(post.id, {
                isCollected: false,
                collections: post.collections
              });
              
              uni.showToast({
                title: '尚未收藏过',
                icon: 'none'
              });
              
              // 刷新获取准确计数
              setTimeout(() => refreshPostStatus(post.id), 500);
              
              return true;
            } else {
              // 其他错误，尝试刷新状态
              await forceRefreshPostStatus(post.id);
              
              // 显示提示
              uni.showToast({
                title: '操作状态已更新',
                icon: 'none'
              });
              
              return false;
            }
          }
          
          throw new Error(result?.message || '操作失败');
        }
      } catch (error) {
        retryCount++;
        console.error(`收藏操作失败 (尝试 ${retryCount}/${MAX_RETRIES + 1}):`, error);
        
        clearTimeout(timeoutId);
        
        if (retryCount <= MAX_RETRIES) {
          console.log(`将在1秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {
          // 处理错误
          await handleApiError(error, () => handlePostCollect(post));
          
          // 回滚状态
          rollbackCollectStatus(post, originalCollected, originalCollections);
          return false;
        }
      }
    }
    
    // 如果执行到这里，说明重试失败
    if (!success) {
      // 回滚状态
      rollbackCollectStatus(post, originalCollected, originalCollections);
      
      // 显示错误提示
      uni.showToast({
        title: '网络异常，请稍后再试',
        icon: 'none'
      });
      
      return false;
    }
    
    return success;
  } catch (error) {
    console.error('收藏操作异常:', error);
    
    // 发生异常时回滚状态（注意此时状态已经被改变，需要取反）
    const originalCollected = !post.isCollected;
    const originalCollections = post.collections + (originalCollected ? 1 : -1);
    rollbackCollectStatus(post, originalCollected, originalCollections);
    
    // 显示错误提示
    uni.showToast({
      title: '操作失败，请稍后再试',
      icon: 'none'
    });
    
    return false;
  }
}

/**
 * 处理评论点赞/取消点赞
 * @param {Object} comment 评论对象
 * @param {String|Number} postId 帖子ID
 * @returns {Promise<boolean>} 操作是否成功
 */
async function handleCommentLike(comment, postId) {
  if (!comment || !comment.id) {
    console.error('无效的评论数据');
    return false;
  }
  
  // 验证用户登录状态
  if (!verifyUserLoginStatus()) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return false;
  }
  
  try {
    // 记录原始状态，用于回滚
    const originalLiked = comment.isLiked;
    const originalLikes = comment.likes || 0;
    
    // 乐观更新UI状态
    comment.isLiked = !originalLiked;
    comment.likes = comment.likes + (comment.isLiked ? 1 : -1);
    
    // 保存到本地存储
    saveCommentLikeStatus(comment.id, comment.isLiked, postId);
    
    console.log(`执行评论${comment.isLiked ? '点赞' : '取消点赞'}操作:`, comment.id);
    
    // 设置请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);
    
    // 准备额外的请求头
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${uni.getStorageSync('token')}`
    };
    
    let retryCount = 0;
    let success = false;
    
    // 实现重试逻辑
    while (retryCount <= MAX_RETRIES && !success) {
      try {
        // 调用API
        const result = await (comment.isLiked 
          ? api.comments.like(comment.id, { headers }) 
          : api.comments.unlike(comment.id, { headers })
        );
        
        clearTimeout(timeoutId);
        
        // 检查结果
        if (result && result.success) {
          console.log(`评论${comment.isLiked ? '点赞' : '取消点赞'}成功:`, comment.id);
          success = true;
          
          // 使用服务器返回的实际状态更新
          if (result.data && result.data.likes !== undefined) {
            comment.likes = result.data.likes;
          }
          
          return true;
        } else {
          // API操作失败，但请求成功发送
          console.error(`评论${comment.isLiked ? '点赞' : '取消点赞'}失败:`, result?.message || '未知错误');
          
          throw new Error(result?.message || '操作失败');
        }
      } catch (error) {
        retryCount++;
        console.error(`评论点赞操作失败 (尝试 ${retryCount}/${MAX_RETRIES + 1}):`, error);
        
        clearTimeout(timeoutId);
        
        if (retryCount <= MAX_RETRIES) {
          console.log(`将在1秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {
          // 处理错误
          await handleApiError(error, () => handleCommentLike(comment, postId));
          
          // 恢复原始状态
          comment.isLiked = originalLiked;
          comment.likes = originalLikes;
          
          // 更新本地存储
          saveCommentLikeStatus(comment.id, originalLiked, postId);
          
          return false;
        }
      }
    }
    
    // 如果执行到这里，说明重试失败
    if (!success) {
      // 恢复原始状态
      comment.isLiked = originalLiked;
      comment.likes = originalLikes;
      
      // 更新本地存储
      saveCommentLikeStatus(comment.id, originalLiked, postId);
      
      // 显示错误提示
      uni.showToast({
        title: '网络异常，请稍后再试',
        icon: 'none'
      });
      
      return false;
    }
    
    return success;
  } catch (error) {
    console.error('评论点赞操作异常:', error);
    
    // 发生异常时回滚状态（注意此时状态已经被改变，需要取反）
    const originalLiked = !comment.isLiked;
    const originalLikes = comment.likes + (originalLiked ? 1 : -1);
    
    // 恢复原始状态
    comment.isLiked = originalLiked;
    comment.likes = originalLikes;
    
    // 更新本地存储
    saveCommentLikeStatus(comment.id, originalLiked, postId);
    
    // 显示错误提示
    uni.showToast({
      title: '操作失败，请稍后再试',
      icon: 'none'
    });
    
    return false;
  }
}

/**
 * 刷新帖子状态
 * @param {String|Number} postId 帖子ID
 * @returns {Promise<Object|null>} 帖子状态对象或null
 */
async function refreshPostStatus(postId) {
  if (!postId) {
    console.error('无效的帖子ID:', postId);
    return null;
  }
  
  try {
    console.log('刷新帖子状态:', postId);
    
    // 添加随机参数，避免缓存
    const randomParam = Math.random().toString(36).substring(2, 15);
    const headers = {
      'Authorization': `Bearer ${uni.getStorageSync('token')}`
    };
    
    // 使用专门的status端点获取帖子状态
    const result = await api.batch.getPostStatus(postId, { 
      data: {
        _r: randomParam,
        _t: Date.now()
      },
      headers 
    });
    
    if (result && result.success && result.data) {
      console.log('获取到最新帖子状态:', result.data);
      
      // 创建状态对象，确保没有默认值覆盖实际状态
      const statusData = {
        isLiked: result.data.isLiked,
        isCollected: result.data.isCollected,
        likes: result.data.likes,
        collections: result.data.collections,
        _timestamp: Date.now()
      };
      
      // 缓存到本地
      uni.setStorageSync(`post_status_${postId}`, JSON.stringify(statusData));
      
      // 更新全局状态
      if (store.mutations.updatePost) {
        store.mutations.updatePost(postId, statusData);
      }
      
      // 更新全局状态映射
      globalPostStateMap.set(postId, {
        isLiked: statusData.isLiked,
        isCollected: statusData.isCollected,
        likes: statusData.likes,
        collections: statusData.collections
      });
      
      // 更新最后更新时间
      lastUpdateTimeMap.set(postId, Date.now());
      
      return statusData;
    } else {
      console.error('获取帖子状态失败:', result ? result.message : '未知错误');
      return null;
    }
  } catch (error) {
    console.error('刷新帖子状态出错:', error);
    return null;
  }
}

/**
 * 回滚帖子点赞状态
 * @param {Object} post 帖子对象
 * @param {Boolean} originalLiked 原始点赞状态
 * @param {Number} originalLikes 原始点赞数
 */
function rollbackLikeStatus(post, originalLiked, originalLikes) {
  console.log('回滚点赞状态:', post.id, '从', post.isLiked, '到', originalLiked);
  
  post.isLiked = originalLiked;
  post.likes = originalLikes;
  
  // 更新本地存储
  savePostLikeStatus(post.id, originalLiked);
  
  // 更新全局状态
  store.mutations.updatePost(post.id, {
    isLiked: originalLiked,
    likes: originalLikes
  });
}

/**
 * 回滚帖子收藏状态
 * @param {Object} post 帖子对象
 * @param {Boolean} originalCollected 原始收藏状态
 * @param {Number} originalCollections 原始收藏数
 */
function rollbackCollectStatus(post, originalCollected, originalCollections) {
  console.log('回滚收藏状态:', post.id, '从', post.isCollected, '到', originalCollected);
  
  post.isCollected = originalCollected;
  post.collections = originalCollections;
  
  // 更新本地存储
  savePostCollectStatus(post.id, originalCollected);
  
  // 更新全局状态
  store.mutations.updatePost(post.id, {
    isCollected: originalCollected,
    collections: originalCollections
  });
}

/**
 * 保存帖子点赞状态到本地存储
 * @param {String|Number} postId 帖子ID
 * @param {Boolean} isLiked 是否点赞
 */
function savePostLikeStatus(postId, isLiked) {
  try {
    const userObj = store.getters.getUser();
    if (!userObj) return;
    
    const user = userObj.user || userObj;
    if (!user || !user.id) return;
    
    const likeData = {
      isLiked,
      postId,
      userId: user.id,
      timestamp: Date.now(),
      expiration: Date.now() + CACHE_EXPIRATION
    };
    
    uni.setStorageSync(`post_like_${postId}`, JSON.stringify(likeData));
  } catch (e) {
    console.error('保存点赞状态出错:', e);
  }
}

/**
 * 保存帖子收藏状态到本地存储
 * @param {String|Number} postId 帖子ID
 * @param {Boolean} isCollected 是否收藏
 */
function savePostCollectStatus(postId, isCollected) {
  try {
    const userObj = store.getters.getUser();
    if (!userObj) return;
    
    const user = userObj.user || userObj;
    if (!user || !user.id) return;
    
    const collectData = {
      isCollected,
      postId,
      userId: user.id,
      timestamp: Date.now(),
      expiration: Date.now() + CACHE_EXPIRATION
    };
    
    uni.setStorageSync(`post_collect_${postId}`, JSON.stringify(collectData));
  } catch (e) {
    console.error('保存收藏状态出错:', e);
  }
}

/**
 * 保存评论点赞状态到本地存储
 * @param {String|Number} commentId 评论ID
 * @param {Boolean} isLiked 是否点赞
 * @param {String|Number} postId 帖子ID，用于分组
 */
function saveCommentLikeStatus(commentId, isLiked, postId) {
  try {
    const userObj = store.getters.getUser();
    if (!userObj) return;
    
    const user = userObj.user || userObj;
    if (!user || !user.id) return;
    
    const likeData = {
      isLiked,
      commentId,
      postId,
      userId: user.id,
      timestamp: Date.now(),
      expiration: Date.now() + CACHE_EXPIRATION
    };
    
    uni.setStorageSync(`comment_like_${commentId}`, JSON.stringify(likeData));
    
    // 同时更新评论点赞状态缓存
    updateCommentLikeCache(commentId, isLiked, postId);
  } catch (e) {
    console.error('保存评论点赞状态出错:', e);
  }
}

/**
 * 更新评论点赞状态缓存
 * @param {String|Number} commentId 评论ID
 * @param {Boolean} isLiked 是否点赞
 * @param {String|Number} postId 帖子ID
 */
function updateCommentLikeCache(commentId, isLiked, postId) {
  try {
    // 获取当前帖子的评论点赞缓存
    const cacheKey = `post_comments_like_${postId}`;
    let commentLikesCache = {};
    
    try {
      const cacheData = uni.getStorageSync(cacheKey);
      if (cacheData) {
        commentLikesCache = JSON.parse(cacheData);
      }
    } catch (e) {
      console.error('获取评论点赞缓存出错:', e);
    }
    
    // 更新缓存
    commentLikesCache[commentId] = {
      isLiked,
      timestamp: Date.now(),
      expiration: Date.now() + CACHE_EXPIRATION
    };
    
    // 保存更新后的缓存
    uni.setStorageSync(cacheKey, JSON.stringify(commentLikesCache));
  } catch (e) {
    console.error('更新评论点赞缓存出错:', e);
  }
}

/**
 * 获取帖子点赞状态
 * @param {String|Number} postId 帖子ID
 * @returns {Boolean|null} 是否点赞，未找到返回null
 */
function getPostLikeStatus(postId) {
  try {
    const userObj = store.getters.getUser();
    if (!userObj) return null;
    
    const user = userObj.user || userObj;
    if (!user || !user.id) return null;
    
    const cacheKey = `post_like_${postId}`;
    const cacheData = uni.getStorageSync(cacheKey);
    
    if (cacheData) {
      const likeData = JSON.parse(cacheData);
      
      // 检查缓存是否过期
      if (likeData.expiration && likeData.expiration > Date.now()) {
        // 检查用户ID是否匹配
        if (likeData.userId === user.id) {
          return likeData.isLiked;
        }
      } else {
        // 缓存过期，清除
        uni.removeStorageSync(cacheKey);
      }
    }
    
    return null;
  } catch (e) {
    console.error('获取点赞状态出错:', e);
    return null;
  }
}

/**
 * 获取帖子收藏状态
 * @param {String|Number} postId 帖子ID
 * @returns {Boolean|null} 是否收藏，未找到返回null
 */
function getPostCollectStatus(postId) {
  try {
    const userObj = store.getters.getUser();
    if (!userObj) return null;
    
    const user = userObj.user || userObj;
    if (!user || !user.id) return null;
    
    const cacheKey = `post_collect_${postId}`;
    const cacheData = uni.getStorageSync(cacheKey);
    
    if (cacheData) {
      const collectData = JSON.parse(cacheData);
      
      // 检查缓存是否过期
      if (collectData.expiration && collectData.expiration > Date.now()) {
        // 检查用户ID是否匹配
        if (collectData.userId === user.id) {
          return collectData.isCollected;
        }
      } else {
        // 缓存过期，清除
        uni.removeStorageSync(cacheKey);
      }
    }
    
    return null;
  } catch (e) {
    console.error('获取收藏状态出错:', e);
    return null;
  }
}

/**
 * 获取评论点赞状态
 * @param {String|Number} commentId 评论ID
 * @returns {Boolean|null} 是否点赞，未找到返回null
 */
function getCommentLikeStatus(commentId) {
  try {
    const userObj = store.getters.getUser();
    if (!userObj) return null;
    
    const user = userObj.user || userObj;
    if (!user || !user.id) return null;
    
    const cacheKey = `comment_like_${commentId}`;
    const cacheData = uni.getStorageSync(cacheKey);
    
    if (cacheData) {
      const likeData = JSON.parse(cacheData);
      
      // 检查缓存是否过期
      if (likeData.expiration && likeData.expiration > Date.now()) {
        // 检查用户ID是否匹配
        if (likeData.userId === user.id) {
          return likeData.isLiked;
        }
      } else {
        // 缓存过期，清除
        uni.removeStorageSync(cacheKey);
      }
    }
    
    return null;
  } catch (e) {
    console.error('获取评论点赞状态出错:', e);
    return null;
  }
}

/**
 * 强制刷新帖子状态
 * @param {String|Number} postId 帖子ID
 * @returns {Promise<Object|null>} 刷新结果
 */
async function forceRefreshPostStatus(postId) {
  if (!postId) {
    console.error('强制刷新帖子状态：无效的帖子ID');
    return null;
  }
  
  console.log(`强制刷新帖子 ${postId} 状态`);
  
  try {
    // 直接从服务器获取最新状态
    const status = await refreshPostStatus(postId);
    
    if (status) {
      // 更新全局状态映射
      globalPostStateMap.set(postId, status);
      
      // 特别关注收藏状态
      if (status.isCollected !== undefined) {
        collectStateMap.set(postId, status.isCollected);
        console.log(`更新收藏状态映射: ${postId} -> ${status.isCollected}`);
      }
      
      // 增加状态版本号
      globalStateVersion++;
      
      // 记录更新时间
      lastUpdateTimeMap.set(postId, Date.now());
      
      // 设置帖子已初始化
      initializedPosts.add(postId);
      
      return status;
    }
  } catch (error) {
    console.error(`强制刷新帖子 ${postId} 状态失败:`, error);
  }
  
  return null;
}

/**
 * 广播帖子状态变更到全局store和UI组件
 */
function broadcastStateChanges() {
  console.log('=== 开始广播状态变化 ===');
  const start = Date.now();
  
  try {
    // 获取所有全局缓存的状态
    const allStates = Array.from(globalPostStateMap.entries());
    const allCollectStates = Array.from(collectStateMap.entries());
    
    if (allStates.length === 0 && allCollectStates.length === 0) {
      console.log('没有缓存的状态可广播');
      return;
    }
    
    console.log(`广播 ${allStates.length} 个帖子状态, ${allCollectStates.length} 个收藏状态`);
    
    // 更新全局store中的所有状态
    allStates.forEach(([postId, status]) => {
      // 确保状态包含必要的字段
      if (status) {
        // 确保收藏状态与专用映射一致
        if (collectStateMap.has(postId)) {
          const collectState = collectStateMap.get(postId);
          if (status.isCollected !== collectState) {
            console.log(`修正帖子 ${postId} 的收藏状态: ${status.isCollected} -> ${collectState}`);
            status.isCollected = collectState;
          }
        }
        
        // 更新到store
        store.mutations.updatePostStatus(postId, {
          ...status,
          _timestamp: Date.now()
        });
        
        console.log(`广播帖子 ${postId} 状态: 点赞=${status.isLiked}, 收藏=${status.isCollected}`);
      }
    });
    
    // 处理仅在收藏映射中存在的状态
    allCollectStates.forEach(([postId, isCollected]) => {
      if (!globalPostStateMap.has(postId)) {
        console.log(`仅收藏映射中存在的帖子 ${postId}, 收藏状态=${isCollected}`);
        
        // 创建最小状态对象
        const minimalState = { isCollected };
        
        // 添加到全局映射
        globalPostStateMap.set(postId, minimalState);
        
        // 更新到store
        store.mutations.updatePostStatus(postId, {
          ...minimalState,
          _timestamp: Date.now()
        });
      }
    });
    
    // 递增全局状态版本号
    globalStateVersion++;
    console.log(`全局状态版本号更新为: ${globalStateVersion}`);
    
    // 尝试保存全局状态到本地存储
    try {
      // 创建状态对象
      const stateSnapshot = {};
      globalPostStateMap.forEach((state, id) => {
        // 确保状态包含所有必要字段
        stateSnapshot[id] = {
          isLiked: state.isLiked,
          isCollected: state.isCollected,
          likes: state.likes,
          collections: state.collections
        };
      });
      
      // 创建收藏状态对象
      const collectSnapshot = {};
      collectStateMap.forEach((isCollected, id) => {
        collectSnapshot[id] = isCollected;
      });
      
      // 更新时间戳并保存
      const globalState = {
        version: globalStateVersion,
        timestamp: Date.now(),
        states: stateSnapshot,
        collectStates: collectSnapshot
      };
      
      // 保存到存储
      uni.setStorageSync('global_post_states', JSON.stringify(globalState));
      
      console.log(`已保存全局状态到存储: ${Object.keys(stateSnapshot).length} 个状态, ${Object.keys(collectSnapshot).length} 个收藏状态`);
    } catch (e) {
      console.error('保存全局状态失败:', e);
    }
  } catch (e) {
    console.error('恢复全局状态失败:', e);
    return { success: false, error: e, reason: '存储访问失败' };
  } finally {
    console.log(`=== 恢复全局状态耗时: ${Date.now() - start}ms ===`);
  }
}

export default {
  handlePostLike,
  handlePostCollect,
  handleCommentLike,
  getPostLikeStatus,
  getPostCollectStatus,
  getCommentLikeStatus,
  refreshPostStatus,
  forceRefreshPostStatus,
  verifyUserLoginStatus,
  setActivePage,
  getActivePage,
  initializePostStatus,
  refreshPostStatuses,
  loadPostStatusesFromStorage,
  synchronizeActivePostsStatus,
  broadcastStateChanges,
  restoreGlobalState,
  savePostLikeStatus,
  savePostCollectStatus,
  ACTIVE_PAGES
}; 