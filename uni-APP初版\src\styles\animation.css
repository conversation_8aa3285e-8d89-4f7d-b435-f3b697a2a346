/* 页面淡入淡出过渡 */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: opacity 0.3s ease;
}
.page-fade-enter-from,
.page-fade-leave-to {
  opacity: 0;
}

/* 上滑淡入 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease-out;
}
.slide-up-enter-from {
  transform: translateY(50px);
  opacity: 0;
}
.slide-up-leave-to {
  transform: translateY(-50px);
  opacity: 0;
}

/* 卡片升起动画 */
.card-pop-enter-active {
  animation: card-pop-in 0.4s ease-out;
}
@keyframes card-pop-in {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  70% {
    transform: scale(1.02);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 按钮点击波纹效果 */
.ripple {
  position: relative;
  overflow: hidden;
}
.ripple:after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.4s, opacity 0.8s;
}
.ripple:active:after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* 开关切换动画 */
.switch-animation {
  transition: all 0.2s ease-in-out;
}

/* 淡入淡出 */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* 滑动入场动画 */
@keyframes slide-in-right {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-up {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 元素hover效果 */
.hover-grow {
  transition: transform 0.2s ease;
}
.hover-grow:hover {
  transform: scale(1.05);
}

/* 列表项动画 */
.list-item-animation {
  animation: slide-in-up 0.4s ease-out forwards;
  animation-delay: calc(var(--i) * 0.05s);
  opacity: 0;
}

/* 点击反馈 */
.tap-feedback:active {
  opacity: 0.7;
  transform: scale(0.98);
} 