const path = require('path');
require('dotenv').config();

const env = process.env.NODE_ENV || 'development';
const databaseConfig = require('./database')[env];
const redisConfig = require('./redis')[env];
const jwtConfig = require('./jwt');
const logger = require('./logger');

module.exports = {
  env,
  port: process.env.PORT || 3000,
  database: databaseConfig,
  redis: redisConfig,
  jwt: jwtConfig,
  logger,
  upload: {
    dir: process.env.UPLOAD_DIR || path.join(__dirname, '../uploads'),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || 5242880, 10), // 默认5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  },
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 204,
    credentials: true
  },
  rateLimiter: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP 15分钟内限制100次请求
    standardHeaders: true,
    legacyHeaders: false,
  }
}; 